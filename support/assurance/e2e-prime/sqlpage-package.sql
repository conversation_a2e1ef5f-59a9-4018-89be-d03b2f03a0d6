-- head SQL defined in `DeploySQL.headSQL` (file:///home/<USER>/workspaces/spry/lib/engine/orchestrate.ts:505:46) (begin)
-- @spry.nature sql @spry.sqlImpact ddl

-- See https://github.com/sqlpage/SQLPage#hosting-sql-files-directly-inside-the-database
-- TODO: generate this using Drizzle Kit

/*------------------------------------------------------------------------------
Table: sqlpage_files

What it stores
  One row per “file” that SQLPage/Spry can read:
    • SQL pages / partials (as text)
    • JSON control files (routes, breadcrumbs, entries, etc.)

Why it exists
  Keeps runtime-ready artifacts inside the DB so routing, nav, and content can be
  queried and transformed with SQL.

How it’s used
  • SQLPage: maps path → contents to render pages.
  • Spry: reads JSON files under spry.d/auto/* for routes/breadcrumbs/entries, etc.

Columns
  path          PK “filename” (e.g. spry/console/index.sql.auto.json)
  contents      The raw text/JSON for the file
  last_modified For cache-invalidation and “pick newest” semantics
------------------------------------------------------------------------------*/
CREATE TABLE IF NOT EXISTS "sqlpage_files" (
  "path" VARCHAR PRIMARY KEY NOT NULL,
  "contents" TEXT NOT NULL,
  "last_modified" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Helpful general index for recency-based joins/filters and debugging
CREATE INDEX IF NOT EXISTS idx_sqlpage_files_last_modified
  ON sqlpage_files (last_modified);

-- ---------------------------------------------------------------------------

-- ---------------------------------------------------------------------------
-- View: spry_annotation
-- Purpose:
--   Flatten annotations from `.source` objects found in:
--     - spry.d/auto/route/**/*.auto.json   (path = $.path)
--     - spry.d/auto/entry/**/*.auto.json   (path = $.webPath)
--   One row per annotation key (e.g., "title", "caption", "description").
--
-- Columns:
--   path        TEXT   -- logical path for the resource (route.path or entry.webPath)
--   namespace   TEXT   -- 'route' | 'entry'
--   annotation  TEXT   -- the key inside `.source` (e.g., 'title')
--   id          TEXT   -- $.id inside the annotation object
--   key         TEXT   -- $.key (e.g., 'route.title')
--   kind        TEXT   -- $.kind (e.g., 'tag')
--   value       TEXT   -- $.value (human string)
--   raw         TEXT   -- $.raw (original tag text)
--   source      JSON   -- $.source (raw JSON with languageId, loc, etc.)
-- ---------------------------------------------------------------------------
DROP VIEW IF EXISTS spry_annotation;
CREATE VIEW spry_annotation AS
WITH route_files AS (
  SELECT
    contents,
    contents ->> '$.path' AS path
  FROM sqlpage_files
  WHERE path GLOB 'spry.d/auto/route/**/*.auto.json'
    AND json_valid(contents)
    AND json_type(contents, '$.path') = 'text'
    AND json_type(contents, '$.".source"') = 'object'
),
entry_files AS (
  SELECT
    contents,
    contents ->> '$.webPath' AS path
  FROM sqlpage_files
  WHERE path GLOB 'spry.d/auto/entry/**/*.auto.json'
    AND json_valid(contents)
    AND json_type(contents, '$.webPath') = 'text'
    AND json_type(contents, '$.".source"') = 'object'
),
route_ann AS (
  SELECT
    rf.path                         AS path_spf,
    CASE
      WHEN substr(rf.path,1,1)='/' THEN rf.path
      ELSE '/' || rf.path
    END                             AS path_href,
    'route'                         AS namespace,
    a.key                           AS annotation,
    a.value ->> '$.id'              AS id,
    a.value ->> '$.key'             AS key,
    a.value ->> '$.kind'            AS kind,
    a.value ->> '$.value'           AS value,
    a.value ->> '$.raw'             AS raw,
    a.value ->  '$.source'          AS source
  FROM route_files rf,
       json_each(rf.contents, '$.".source"') AS a
),
entry_ann AS (
  SELECT
    ef.path                         AS path,
    CASE
      WHEN substr(ef.path,1,1)='/' THEN ef.path
      ELSE '/' || ef.path
    END                             AS path_href,
    'entry'                         AS namespace,
    a.key                           AS annotation,
    a.value ->> '$.id'              AS id,
    a.value ->> '$.key'             AS key,
    a.value ->> '$.kind'            AS kind,
    a.value ->> '$.value'           AS value,
    a.value ->> '$.raw'             AS raw,
    a.value ->  '$.source'          AS source
  FROM entry_files ef,
       json_each(ef.contents, '$.".source"') AS a
)
SELECT * FROM route_ann
UNION ALL
SELECT * FROM entry_ann;

CREATE INDEX IF NOT EXISTS idx_route_source_json
  ON sqlpage_files (json_extract(contents, '$.".source"'))
  WHERE path GLOB 'spry.d/auto/route/**/*.auto.json' AND json_valid(contents);

CREATE INDEX IF NOT EXISTS idx_entry_source_json
  ON sqlpage_files (json_extract(contents, '$.".source"'))
  WHERE path GLOB 'spry.d/auto/entry/**/*.auto.json' AND json_valid(contents);

CREATE INDEX IF NOT EXISTS idx_route_path
  ON sqlpage_files (contents ->> '$.path')
  WHERE path GLOB 'spry.d/auto/route/**/*.auto.json';

CREATE INDEX IF NOT EXISTS idx_entry_webpath
  ON sqlpage_files (contents ->> '$.webPath')
  WHERE path GLOB 'spry.d/auto/entry/**/*.auto.json';

/*------------------------------------------------------------------------------
View: spry_route

Purpose
  Projects one row per route from files under spry.d/auto/route/**\/*.auto.json.

Shape (selected)
  path, title, caption, url, description, elaboration, plus provenance:
  spf_path (source file path), spf_last_modified.

Notes
  • Expects each file’s contents to be a JSON object with the route fields.
  • Filtered indexes below speed up discovery and path lookups.
------------------------------------------------------------------------------*/
DROP VIEW IF EXISTS spry_route;
CREATE VIEW spry_route AS
WITH f AS (
  SELECT
    path          AS spf_path,
    last_modified AS spf_last_modified,
    contents,
    substr(
      path,
      length('spry.d/auto/route/') + 1,
      length(path) - length('spry.d/auto/route/') - length('.auto.json')
    )             AS path_spf_target
  FROM sqlpage_files
  WHERE path GLOB 'spry.d/auto/route/**/*.auto.json'
    AND json_valid(contents)
)
SELECT
  contents ->> '$.path'                AS "path_spf",
  path_spf_target                      AS "path_spf_target",
  CASE
    WHEN substr(path_spf_target,1,1)='/' THEN path_spf_target
    ELSE '/' || path_spf_target
  END                                  AS path_href,  
  contents ->> '$.pathBasename'        AS "path_basename",
  contents ->> '$.pathBasenameNoExtn'  AS "path_basename_no_extn",
  contents ->> '$.pathDirname'         AS "path_dirname",
  contents ->> '$.pathExtnTerminal'    AS "path_extn_terminal",
  contents ->  '$.pathExtns'           AS "path_extns",

  contents ->> '$.caption'             AS "caption",
  contents ->> '$.siblingOrder'        AS "sibling_order",
  contents ->> '$.url'                 AS "url",
  contents ->> '$.title'               AS "title",
  contents ->> '$.abbreviatedCaption'  AS "abbreviated_caption",
  contents ->> '$.description'         AS "description",
  contents ->  '$.elaboration'         AS "elaboration",

  spf_path,
  spf_last_modified
FROM f
WHERE json_type(contents, '$.path') = 'text';

-- fast lookups by route path
CREATE INDEX IF NOT EXISTS idx_route_json_path_flat
  ON sqlpage_files (contents ->> '$.path')
  WHERE path GLOB 'spry.d/auto/route/**/*.auto.json';

-- help queries that scan JSON content
CREATE INDEX IF NOT EXISTS idx_route_json_scan
  ON sqlpage_files (json(contents))  -- or (contents -> '$') if you prefer
  WHERE path GLOB 'spry.d/auto/route/**/*.auto.json';

/*------------------------------------------------------------------------------
View: spry_route_crumb

Purpose
  Emits one row per breadcrumb “crumb” from files under
  spry.d/auto/breadcrumbs/**\/*.auto.json (each file is an array of objects).

Shape (selected)
  path (derived from filename), crumb_index, href_* (canonical/index/trailingSlash),
  node_* (virtual/basename/path), plus source provenance.

Notes
  • Does not traverse children/payloads; reads only top-level keys of each array item.
  • Filtered indexes below speed up discovery and JSON scans.
------------------------------------------------------------------------------*/
DROP VIEW IF EXISTS spry_route_crumb;
CREATE VIEW spry_route_crumb AS
WITH files AS (
  SELECT
    f.path          AS src_path,
    f.last_modified AS src_last_modified,
    f.contents,
    -- filename-derived logical path (strip prefix/suffix)
    substr(
      f.path,
      length('spry.d/auto/breadcrumbs/') + 1,
      length(f.path) - length('spry.d/auto/breadcrumbs/') - length('.auto.json')
    ) AS path_spf_target
  FROM sqlpage_files AS f
  WHERE f.path GLOB 'spry.d/auto/breadcrumbs/**/*.auto.json'
    AND json_valid(f.contents)
),
-- One row per crumb (array element)
raw AS (
  SELECT
    files.src_path,
    files.src_last_modified,
    files.path_spf_target,                     -- qualified to avoid json_each.path name
    CAST(c.key AS INTEGER) AS crumb_index,
    c.value                AS crumb
  FROM files
  JOIN json_each(files.contents) AS c
)
SELECT
  -- logical path derived from the filename
  path_spf_target AS path_spf,
  CASE
    WHEN substr(path_spf_target,1,1)='/' THEN path_spf_target
    ELSE '/' || path_spf_target
  END                               AS path_href,
  crumb_index,

  -- hrefs.* (top-level)
  crumb ->> '$.hrefs.canonical'     AS href_canonical,
  crumb ->> '$.hrefs.index'         AS href_index,
  crumb ->> '$.hrefs.trailingSlash' AS href_trailing_slash,

  -- node.* (top-level)
  crumb ->> '$.node.virtual'  AS node_virtual,
  crumb ->> '$.node.basename' AS node_basename,
  crumb ->> '$.node.path'     AS node_path,

  -- provenance
  src_path            AS breadcrumbs_source_path,
  src_last_modified   AS breadcrumbs_source_last_modified
FROM raw
WHERE json_type(crumb, '$.node.path') = 'text'
ORDER BY src_path, crumb_index;

-- Breadcrumb discovery + JSON scan accelerators
CREATE INDEX IF NOT EXISTS idx_breadcrumbs_dir
  ON sqlpage_files (path)
  WHERE path GLOB 'spry.d/auto/breadcrumbs/**/*.auto.json';

CREATE INDEX IF NOT EXISTS idx_breadcrumbs_json
  ON sqlpage_files (json_extract(contents))
  WHERE path GLOB 'spry.d/auto/breadcrumbs/**/*.auto.json';

-- ---------------------------------------------------------------------------
-- View: spry_route_edge
-- Purpose: Flatten prebuilt edges (parent → child) into rows, newest-wins.
-- Inputs:  sqlpage_files where path GLOB 'spry.d/auto/route/*edges*.auto.json'
-- Columns: parent, child, edges_source_path, edges_source_last_modified
-- ---------------------------------------------------------------------------
DROP VIEW IF EXISTS spry_route_edge;
CREATE VIEW spry_route_edge AS
WITH files AS (
  SELECT
    f.path          AS src_path,
    f.last_modified AS src_last_modified,
    f.contents
  FROM sqlpage_files AS f
  WHERE f.path GLOB 'spry.d/auto/route/*edges*.auto.json'
    AND json_valid(f.contents)
),
edges_raw AS (
  SELECT
    src_path,
    src_last_modified,
    json_extract(e.value, '$.parent') AS parent_path_spf,
    json_extract(e.value, '$.child')  AS child_path_spf
  FROM files, json_each(files.contents) AS e
  WHERE json_type(e.value, '$.parent') = 'text'
    AND json_type(e.value, '$.child')  = 'text'
),
ranked AS (
  SELECT
    parent_path_spf,
    child_path_spf,
    src_path,
    src_last_modified,
    ROW_NUMBER() OVER (
      PARTITION BY parent_path_spf, child_path_spf
      ORDER BY src_last_modified DESC, src_path DESC
    ) AS rn
  FROM edges_raw
)
SELECT
  parent_path_spf,
  child_path_spf,
  CASE
    WHEN substr(parent_path_spf,1,1)='/' THEN parent_path_spf
    ELSE '/' || parent_path_spf
  END               AS parent_path_href,
  CASE
    WHEN substr(child_path_spf,1,1)='/' THEN child_path_spf
    ELSE '/' || child_path_spf
  END               AS child_path_href,
  src_path          AS edges_source_path,
  src_last_modified AS edges_source_last_modified
FROM ranked
WHERE rn = 1;

-- Edges discovery + JSON scan accelerators on base table
CREATE INDEX IF NOT EXISTS idx_route_edges_dir
  ON sqlpage_files (path)
  WHERE path GLOB 'spry.d/auto/route/*edges*.auto.json';

CREATE INDEX IF NOT EXISTS idx_route_edges_json
  ON sqlpage_files (json_extract(contents))
  WHERE path GLOB 'spry.d/auto/route/*edges*.auto.json';

-- ---------------------------------------------------------------------------
-- View: spry_route_child
-- Purpose: Join edges to spry_route for child metadata.
-- Outputs: parent, child, c.* (all columns from spry_route for the child),
--          edges_source_path, edges_source_last_modified
-- ---------------------------------------------------------------------------
DROP VIEW IF EXISTS spry_route_child;
CREATE VIEW spry_route_child AS
SELECT
  e.parent_path_spf,
  e.parent_path_href,
  c.*,
  e.edges_source_path,
  e.edges_source_last_modified
FROM spry_route_edge AS e
JOIN spry_route     AS c
  ON c."path_spf" = e.child_path_spf
ORDER BY e.parent_path_spf, c."path_spf";

-- Speed extraction of the raw ".source" annotations in entry files
CREATE INDEX IF NOT EXISTS idx_entry_source_json
  ON sqlpage_files (json_extract(contents, '$.".source"'))
  WHERE path GLOB 'spry.d/auto/entry/**/*.auto.json'
    AND json_valid(contents);

-- Speed extraction of the raw ".source" annotations in route files
CREATE INDEX IF NOT EXISTS idx_route_source_json
  ON sqlpage_files (json_extract(contents, '$.".source"'))
  WHERE path GLOB 'spry.d/auto/route/**/*.auto.json'
    AND json_valid(contents);

-- ---------------------------------------------------------------------------
-- View: spry_entry
-- Purpose: Surface entry metadata + annotations for files under spry.d/auto/entry/**
-- Columns:
--   path           ← contents.webPath
--   nature         ← contents.nature
--   rel_fs_path    ← contents.relFsPath
--   entry_source_path, entry_source_last_modified (provenance)
-- ---------------------------------------------------------------------------
DROP VIEW IF EXISTS spry_entry;
CREATE VIEW spry_entry AS
WITH files AS (
  SELECT
    f.path          AS src_path,
    f.last_modified AS src_last_modified,
    f.contents,
    substr(
      f.path,
      length('spry.d/auto/entry/') + 1,
      length(f.path) - length('spry.d/auto/entry/') - length('.auto.json')
    ) AS path_spf_target
  FROM sqlpage_files AS f
  WHERE f.path GLOB 'spry.d/auto/entry/**/*.auto.json'
    AND json_valid(f.contents)
)
SELECT
  files.contents ->> '$.webPath'   AS path_spf,
  CASE
    WHEN substr(path_spf_target,1,1)='/' THEN path_spf_target
    ELSE '/' || path_spf_target
  END                              AS path_href,
  files.contents ->> '$.nature'    AS nature,
  files.contents ->> '$.relFsPath' AS rel_fs_path,
  files.src_path            AS entry_source_path,
  files.src_last_modified   AS entry_source_last_modified
FROM files
WHERE json_type(files.contents, '$.webPath') = 'text';

-- Fast lookups by relFsPath
CREATE INDEX IF NOT EXISTS idx_entry_relfs
  ON sqlpage_files (json_extract(contents, '$.webPath'))
  WHERE path GLOB 'spry.d/auto/entry/**/*.auto.json';

-- @spry.nature sql @spry.sqlImpact dml

-- Spry schema information is stored in sqlpage_files as a single-row-per-schema
-- catalog with path 'spry.d/info-schema.auto.json' (where 'main' is schhema).
-- Stores a prettifiedJSON graph of the entire SQLite schema (tables, columns, 
-- indexes, foreign keys, views, triggers, and derived relations). These views 
-- project that JSON back into relational form for easy querying. Filter by 
-- schema_name in WHERE clauses (e.g., WHERE s.path = 'spry.d/info-schema.auto.json'). 
-- Requires SQLite JSON1.

-- Populate with a comprehensive JSON graph of the current schema
INSERT OR REPLACE INTO sqlpage_files (path, contents)
VALUES (
  'spry.d/info-schema.auto.json',
  json_pretty(
    json_object(
      'schema_name', 'main',
      'generated_on', datetime('now'),
      'sqlite_version', sqlite_version(),

      -- Attached databases
      'databases',
      (SELECT json_group_array(json_object('seq', seq, 'db_name', name, 'db_file', file))
         FROM pragma_database_list),

      -- Available collations
      'collations',
      (SELECT json_group_array(json_object('seq', seq, 'name', name))
         FROM pragma_collation_list),

      -- Tables keyed by name, with nested columns, indexes, FKs, triggers
      'tables',
      (
        SELECT json_group_object(
                 tl.name,
                 json_object(
                   'type', tl.type,                 -- 'table'
                   'strict', tl.strict,
                   'without_rowid', 0,              -- not exposed by pragma_table_list; set 0 by default
                   'ncol', tl.ncol,
                   'sql', (SELECT s.sql FROM sqlite_schema AS s WHERE s.type='table' AND s.name=tl.name),

                   'columns',
                   (SELECT json_group_array(
                             json_object(
                               'cid', x.cid,
                               'name', x.name,
                               'type', x.type,
                               'notnull', x."notnull",
                               'dflt_value', x.dflt_value,
                               'pk', x.pk,
                               'hidden', x.hidden
                             )
                           )
                      FROM pragma_table_xinfo(tl.name) AS x),

                   'indexes',
                   (SELECT json_group_array(
                             json_object(
                               'name', il.name,
                               'origin', il.origin,          -- 'c','u','pk'
                               'unique', il."unique",
                               'partial', il.partial,
                               'where',
                                 (SELECT s.sql FROM sqlite_schema AS s
                                   WHERE s.type='index' AND s.name=il.name),
                               'columns',
                                 (SELECT json_group_array(
                                           json_object(
                                             'seqno', ixi.seqno,
                                             'cid', ixi.cid,
                                             'name', ixi.name,
                                             'desc', ixi."desc",
                                             'coll', ixi.coll,
                                             'key', ixi."key"
                                           )
                                         )
                                    FROM pragma_index_xinfo(il.name) AS ixi)
                             )
                           )
                      FROM pragma_index_list(tl.name) AS il),

                   'foreign_keys',
                   (SELECT json_group_array(
                             json_object(
                               'id', fk.id,
                               'seq', fk.seq,
                               'from', fk."from",
                               'to', fk."to",
                               'table', fk."table",
                               'on_update', fk.on_update,
                               'on_delete', fk.on_delete,
                               'match', fk."match"
                             )
                           )
                      FROM pragma_foreign_key_list(tl.name) AS fk),

                   'triggers',
                   (SELECT json_group_array(
                             json_object(
                               'name', t.name,
                               'sql',  t.sql
                             )
                           )
                      FROM sqlite_schema AS t
                     WHERE t.type='trigger' AND t.tbl_name=tl.name)
                 )
               )
          FROM pragma_table_list AS tl
         WHERE tl.type='table' AND tl.name NOT LIKE 'sqlite_%'
      ),

      -- Views keyed by name
      'views',
      (
        SELECT json_group_object(
                 v.name,
                 json_object(
                   'type', 'view',
                   'sql',  v.sql,
                   'dependencies', json('[]') -- placeholder (dependency parsing is non-trivial)
                 )
               )
          FROM sqlite_schema AS v
         WHERE v.type='view' AND v.name NOT LIKE 'sqlite_%'
      ),

      -- Virtual tables keyed by name (basic capture)
      'virtual_tables',
      (
        SELECT json_group_object(
                 tl.name,
                 json_object(
                   'type', tl.type,  -- 'virtual'
                   'sql', (SELECT s.sql FROM sqlite_schema AS s WHERE s.type='table' AND s.name=tl.name)
                 )
               )
          FROM pragma_table_list AS tl
         WHERE tl.type='virtual' AND tl.name NOT LIKE 'sqlite_%'
      ),

      -- Triggers keyed by name (top-level convenience)
      'triggers',
      (
        SELECT json_group_object(
                 t.name,
                 json_object(
                   'table', t.tbl_name,
                   'sql',   t.sql
                 )
               )
          FROM sqlite_schema AS t
         WHERE t.type='trigger' AND t.name NOT LIKE 'sqlite_%'
      ),

      -- Relations derived from all foreign keys
      'relations',
      (
        SELECT json_group_array(
                 json_object(
                   'name', printf('%s_%s_%s_%s', fk.tbl_name, fk."from", fk."table", fk."to"),
                   'from_table', fk.tbl_name,
                   'from_columns', json_array(fk."from"),
                   'to_table', fk."table",
                   'to_columns', json_array(fk."to"),
                   'type', 'many_to_one',
                   'on_update', fk.on_update,
                   'on_delete', fk.on_delete,
                   'match', fk."match"
                 )
               )
          FROM (
                 SELECT
                   tbl.name AS tbl_name,
                   fk."from",
                   fk."to",
                   fk."table",
                   fk.on_update,
                   fk.on_delete,
                   fk."match"
                 FROM sqlite_schema AS tbl,
                      pragma_foreign_key_list(tbl.name) AS fk
                 WHERE tbl.type='table' AND tbl.name NOT LIKE 'sqlite_%'
               ) AS fk
      )
    )
  )
);

-- Tables (one row per table)
DROP VIEW IF EXISTS spry_schema_info_table;
CREATE VIEW IF NOT EXISTS spry_schema_info_table AS
SELECT
  'main'                                    AS schema_name,
  t.key                                     AS table_name,
  json_extract(t.value,'$.type')            AS type,
  json_extract(t.value,'$.ncol')            AS ncol,
  json_extract(t.value,'$.strict')          AS strict,
  json_extract(t.value,'$.without_rowid')   AS without_rowid,
  json_extract(t.value,'$.sql')             AS definition_sql
FROM sqlpage_files AS s,
     json_each(s.contents, '$.tables') AS t
WHERE s.path = 'spry.d/info-schema.auto.json';

-- Table columns (one row per column per table)
DROP VIEW IF EXISTS spry_schema_info_table_column;
CREATE VIEW IF NOT EXISTS spry_schema_info_table_column AS
SELECT
  'main'                                        AS schema_name,
  t.key                                         AS table_name,
  json_extract(c.value,'$.cid')                 AS cid,
  json_extract(c.value,'$.name')                AS column_name,
  json_extract(c.value,'$.type')                AS column_type,
  json_extract(c.value,'$.notnull')             AS not_null,
  json_extract(c.value,'$.dflt_value')          AS dflt_value,
  json_extract(c.value,'$.pk')                  AS part_of_pk,
  json_extract(c.value,'$.hidden')              AS hidden
FROM sqlpage_files AS s,
     json_each(s.contents, '$.tables') AS t,
     json_each(t.value, '$.columns')           AS c
WHERE s.path = 'spry.d/info-schema.auto.json';

-- Views (one row per view)
DROP VIEW IF EXISTS spry_schema_info_view;
CREATE VIEW IF NOT EXISTS spry_schema_info_view AS
SELECT
  'main'                           AS schema_name,
  v.key                            AS view_name,
  json_extract(v.value,'$.type')   AS type,
  json_extract(v.value,'$.sql')    AS definition_sql
FROM sqlpage_files AS s,
     json_each(s.contents, '$.views') AS v
WHERE s.path = 'spry.d/info-schema.auto.json';

-- View columns (if your schema_graph_json includes a $.views[*].columns array)
DROP VIEW IF EXISTS spry_schema_info_view_column;
CREATE VIEW IF NOT EXISTS spry_schema_info_view_column AS
SELECT
  'main'                          AS schema_name,
  v.key                           AS view_name,
  json_extract(vc.value,'$.cid')  AS cid,
  json_extract(vc.value,'$.name') AS column_name,
  json_extract(vc.value,'$.type') AS column_type,
  json_extract(vc.value,'$.notnull') AS not_null,
  json_extract(vc.value,'$.dflt_value') AS dflt_value
FROM sqlpage_files AS s,
     json_each(s.contents, '$.views') AS v
LEFT JOIN json_each(v.value, '$.columns') AS vc ON 1=1
WHERE s.path = 'spry.d/info-schema.auto.json';

-- Indexes (one row per index per table)
DROP VIEW IF EXISTS spry_schema_info_index;
CREATE VIEW IF NOT EXISTS spry_schema_info_index AS
SELECT
  'main'                                       AS schema_name,
  t.key                                        AS table_name,
  json_extract(i.value,'$.name')               AS index_name,
  json_extract(i.value,'$.origin')             AS origin,      -- 'c','u','pk'
  json_extract(i.value,'$.unique')             AS is_unique,
  json_extract(i.value,'$.partial')            AS is_partial,
  json_extract(i.value,'$.where')              AS definition_sql
FROM sqlpage_files AS s,
     json_each(s.contents, '$.tables')         AS t,
     json_each(t.value, '$.indexes')           AS i
WHERE s.path = 'spry.d/info-schema.auto.json';

-- Index columns (one row per column per index)
DROP VIEW IF EXISTS spry_schema_info_index_column;
CREATE VIEW IF NOT EXISTS spry_schema_info_index_column AS
SELECT
  'main'                                        AS schema_name,
  t.key                                         AS table_name,
  json_extract(i.value,'$.name')                AS index_name,
  json_extract(ic.value,'$.seqno')              AS seqno,
  json_extract(ic.value,'$.cid')                AS cid,
  json_extract(ic.value,'$.name')               AS column_name,
  json_extract(ic.value,'$.desc')               AS is_desc,
  json_extract(ic.value,'$.coll')               AS collation_name,
  json_extract(ic.value,'$.key')                AS is_key_column
FROM sqlpage_files AS s,
     json_each(s.contents, '$.tables')          AS t,
     json_each(t.value, '$.indexes')            AS i,
     json_each(i.value, '$.columns')            AS ic
WHERE s.path = 'spry.d/info-schema.auto.json';

-- Foreign keys (one row per referencing column)
DROP VIEW IF EXISTS spry_schema_info_foreign_key;
CREATE VIEW IF NOT EXISTS spry_schema_info_foreign_key AS
SELECT
  'main'                                   AS schema_name,
  t.key                                    AS table_name,
  json_extract(fk.value,'$.id')            AS fk_id,
  json_extract(fk.value,'$.seq')           AS seq,
  json_extract(fk.value,'$.from')          AS from_column,
  json_extract(fk.value,'$.to')            AS to_column,
  json_extract(fk.value,'$.table')         AS ref_table,
  json_extract(fk.value,'$.on_update')     AS on_update,
  json_extract(fk.value,'$.on_delete')     AS on_delete,
  json_extract(fk.value,'$.match')         AS match
FROM sqlpage_files AS s,
     json_each(s.contents, '$.tables')     AS t,
     json_each(t.value, '$.foreign_keys')  AS fk
WHERE s.path = 'spry.d/info-schema.auto.json';

-- Table triggers (one row per trigger per table)
DROP VIEW IF EXISTS spry_schema_info_table_trigger;
CREATE VIEW IF NOT EXISTS spry_schema_info_table_trigger AS
SELECT
  'main'                                AS schema_name,
  t.key                                 AS table_name,
  json_extract(tr.value,'$.name')       AS trigger_name,
  json_extract(tr.value,'$.sql')        AS definition_sql
FROM sqlpage_files AS s,
     json_each(s.contents, '$.tables')  AS t,
     json_each(t.value, '$.triggers')   AS tr
WHERE s.path = 'spry.d/info-schema.auto.json';

-- Top-level triggers (if captured under $.triggers object)
DROP VIEW IF EXISTS spry_schema_info_trigger;
CREATE VIEW IF NOT EXISTS spry_schema_info_trigger AS
SELECT
  'main'                                AS schema_name,
  trg.key                               AS trigger_name,
  json_extract(trg.value,'$.table')     AS table_name,
  json_extract(trg.value,'$.sql')       AS definition_sql
FROM sqlpage_files AS s,
     json_each(s.contents, '$.triggers') AS trg
WHERE s.path = 'spry.d/info-schema.auto.json';

-- Relations derived in schema_graph_json (one row per relation)
DROP VIEW IF EXISTS spry_schema_info_relation;
CREATE VIEW IF NOT EXISTS spry_schema_info_relation AS
SELECT
  'main'                                  AS schema_name,
  json_extract(r.value,'$.name')          AS relation_name,
  json_extract(r.value,'$.from_table')    AS from_table,
  json_extract(r.value,'$.to_table')      AS to_table,
  json_extract(r.value,'$.type')          AS relation_type,
  json_extract(r.value,'$.on_update')     AS on_update,
  json_extract(r.value,'$.on_delete')     AS on_delete,
  json_extract(r.value,'$.match')         AS match,
  json_extract(r.value,'$.from_columns')  AS from_columns_json,
  json_extract(r.value,'$.to_columns')    AS to_columns_json
FROM sqlpage_files AS s,
     json_each(s.contents, '$.relations') AS r
WHERE s.path = 'spry.d/info-schema.auto.json';

-- head SQL defined in `DeploySQL.headSQL` (file:///home/<USER>/workspaces/spry/lib/engine/orchestrate.ts:509:46) (end)
-- sqlpage_files rows --
delete from "sqlpage_files" where "sqlpage_files"."path" = 'deno.json';
insert into "sqlpage_files" ("path", "contents", "last_modified") values ('deno.json', '{
  "imports": {
    "@libsql/client": "npm:@libsql/client@^0.15.14",
    "drizzle-kit": "npm:drizzle-kit@^0.31.4",
    "drizzle-orm": "npm:drizzle-orm@^0.44.5"
  },
  "tasks": {
    "check": "deno check ../../../lib/engine"
  }
}', CURRENT_TIMESTAMP);
delete from "sqlpage_files" where "sqlpage_files"."path" = 'sqlpage-package.sql';
insert into "sqlpage_files" ("path", "contents", "last_modified") values ('sqlpage-package.sql', '-- head SQL defined in `DeploySQL.headSQL` (file:///home/<USER>/workspaces/spry/lib/engine/orchestrate.ts:505:46) (begin)
-- @spry.nature sql @spry.sqlImpact ddl

-- See https://github.com/sqlpage/SQLPage#hosting-sql-files-directly-inside-the-database
-- TODO: generate this using Drizzle Kit

/*------------------------------------------------------------------------------
Table: sqlpage_files

What it stores
  One row per “file” that SQLPage/Spry can read:
    • SQL pages / partials (as text)
    • JSON control files (routes, breadcrumbs, entries, etc.)

Why it exists
  Keeps runtime-ready artifacts inside the DB so routing, nav, and content can be
  queried and transformed with SQL.

How it’s used
  • SQLPage: maps path → contents to render pages.
  • Spry: reads JSON files under spry.d/auto/* for routes/breadcrumbs/entries, etc.

Columns
  path          PK “filename” (e.g. spry/console/index.sql.auto.json)
  contents      The raw text/JSON for the file
  last_modified For cache-invalidation and “pick newest” semantics
------------------------------------------------------------------------------*/
CREATE TABLE IF NOT EXISTS "sqlpage_files" (
  "path" VARCHAR PRIMARY KEY NOT NULL,
  "contents" TEXT NOT NULL,
  "last_modified" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Helpful general index for recency-based joins/filters and debugging
CREATE INDEX IF NOT EXISTS idx_sqlpage_files_last_modified
  ON sqlpage_files (last_modified);

-- ---------------------------------------------------------------------------

-- ---------------------------------------------------------------------------
-- View: spry_annotation
-- Purpose:
--   Flatten annotations from `.source` objects found in:
--     - spry.d/auto/route/**/*.auto.json   (path = $.path)
--     - spry.d/auto/entry/**/*.auto.json   (path = $.webPath)
--   One row per annotation key (e.g., "title", "caption", "description").
--
-- Columns:
--   path        TEXT   -- logical path for the resource (route.path or entry.webPath)
--   namespace   TEXT   -- ''route'' | ''entry''
--   annotation  TEXT   -- the key inside `.source` (e.g., ''title'')
--   id          TEXT   -- $.id inside the annotation object
--   key         TEXT   -- $.key (e.g., ''route.title'')
--   kind        TEXT   -- $.kind (e.g., ''tag'')
--   value       TEXT   -- $.value (human string)
--   raw         TEXT   -- $.raw (original tag text)
--   source      JSON   -- $.source (raw JSON with languageId, loc, etc.)
-- ---------------------------------------------------------------------------
DROP VIEW IF EXISTS spry_annotation;
CREATE VIEW spry_annotation AS
WITH route_files AS (
  SELECT
    contents,
    contents ->> ''$.path'' AS path
  FROM sqlpage_files
  WHERE path GLOB ''spry.d/auto/route/**/*.auto.json''
    AND json_valid(contents)
    AND json_type(contents, ''$.path'') = ''text''
    AND json_type(contents, ''$.".source"'') = ''object''
),
entry_files AS (
  SELECT
    contents,
    contents ->> ''$.webPath'' AS path
  FROM sqlpage_files
  WHERE path GLOB ''spry.d/auto/entry/**/*.auto.json''
    AND json_valid(contents)
    AND json_type(contents, ''$.webPath'') = ''text''
    AND json_type(contents, ''$.".source"'') = ''object''
),
route_ann AS (
  SELECT
    rf.path                         AS path_spf,
    CASE
      WHEN substr(rf.path,1,1)=''/'' THEN rf.path
      ELSE ''/'' || rf.path
    END                             AS path_href,
    ''route''                         AS namespace,
    a.key                           AS annotation,
    a.value ->> ''$.id''              AS id,
    a.value ->> ''$.key''             AS key,
    a.value ->> ''$.kind''            AS kind,
    a.value ->> ''$.value''           AS value,
    a.value ->> ''$.raw''             AS raw,
    a.value ->  ''$.source''          AS source
  FROM route_files rf,
       json_each(rf.contents, ''$.".source"'') AS a
),
entry_ann AS (
  SELECT
    ef.path                         AS path,
    CASE
      WHEN substr(ef.path,1,1)=''/'' THEN ef.path
      ELSE ''/'' || ef.path
    END                             AS path_href,
    ''entry''                         AS namespace,
    a.key                           AS annotation,
    a.value ->> ''$.id''              AS id,
    a.value ->> ''$.key''             AS key,
    a.value ->> ''$.kind''            AS kind,
    a.value ->> ''$.value''           AS value,
    a.value ->> ''$.raw''             AS raw,
    a.value ->  ''$.source''          AS source
  FROM entry_files ef,
       json_each(ef.contents, ''$.".source"'') AS a
)
SELECT * FROM route_ann
UNION ALL
SELECT * FROM entry_ann;

CREATE INDEX IF NOT EXISTS idx_route_source_json
  ON sqlpage_files (json_extract(contents, ''$.".source"''))
  WHERE path GLOB ''spry.d/auto/route/**/*.auto.json'' AND json_valid(contents);

CREATE INDEX IF NOT EXISTS idx_entry_source_json
  ON sqlpage_files (json_extract(contents, ''$.".source"''))
  WHERE path GLOB ''spry.d/auto/entry/**/*.auto.json'' AND json_valid(contents);

CREATE INDEX IF NOT EXISTS idx_route_path
  ON sqlpage_files (contents ->> ''$.path'')
  WHERE path GLOB ''spry.d/auto/route/**/*.auto.json'';

CREATE INDEX IF NOT EXISTS idx_entry_webpath
  ON sqlpage_files (contents ->> ''$.webPath'')
  WHERE path GLOB ''spry.d/auto/entry/**/*.auto.json'';

/*------------------------------------------------------------------------------
View: spry_route

Purpose
  Projects one row per route from files under spry.d/auto/route/**\/*.auto.json.

Shape (selected)
  path, title, caption, url, description, elaboration, plus provenance:
  spf_path (source file path), spf_last_modified.

Notes
  • Expects each file’s contents to be a JSON object with the route fields.
  • Filtered indexes below speed up discovery and path lookups.
------------------------------------------------------------------------------*/
DROP VIEW IF EXISTS spry_route;
CREATE VIEW spry_route AS
WITH f AS (
  SELECT
    path          AS spf_path,
    last_modified AS spf_last_modified,
    contents,
    substr(
      path,
      length(''spry.d/auto/route/'') + 1,
      length(path) - length(''spry.d/auto/route/'') - length(''.auto.json'')
    )             AS path_spf_target
  FROM sqlpage_files
  WHERE path GLOB ''spry.d/auto/route/**/*.auto.json''
    AND json_valid(contents)
)
SELECT
  contents ->> ''$.path''                AS "path_spf",
  path_spf_target                      AS "path_spf_target",
  CASE
    WHEN substr(path_spf_target,1,1)=''/'' THEN path_spf_target
    ELSE ''/'' || path_spf_target
  END                                  AS path_href,  
  contents ->> ''$.pathBasename''        AS "path_basename",
  contents ->> ''$.pathBasenameNoExtn''  AS "path_basename_no_extn",
  contents ->> ''$.pathDirname''         AS "path_dirname",
  contents ->> ''$.pathExtnTerminal''    AS "path_extn_terminal",
  contents ->  ''$.pathExtns''           AS "path_extns",

  contents ->> ''$.caption''             AS "caption",
  contents ->> ''$.siblingOrder''        AS "sibling_order",
  contents ->> ''$.url''                 AS "url",
  contents ->> ''$.title''               AS "title",
  contents ->> ''$.abbreviatedCaption''  AS "abbreviated_caption",
  contents ->> ''$.description''         AS "description",
  contents ->  ''$.elaboration''         AS "elaboration",

  spf_path,
  spf_last_modified
FROM f
WHERE json_type(contents, ''$.path'') = ''text'';

-- fast lookups by route path
CREATE INDEX IF NOT EXISTS idx_route_json_path_flat
  ON sqlpage_files (contents ->> ''$.path'')
  WHERE path GLOB ''spry.d/auto/route/**/*.auto.json'';

-- help queries that scan JSON content
CREATE INDEX IF NOT EXISTS idx_route_json_scan
  ON sqlpage_files (json(contents))  -- or (contents -> ''$'') if you prefer
  WHERE path GLOB ''spry.d/auto/route/**/*.auto.json'';

/*------------------------------------------------------------------------------
View: spry_route_crumb

Purpose
  Emits one row per breadcrumb “crumb” from files under
  spry.d/auto/breadcrumbs/**\/*.auto.json (each file is an array of objects).

Shape (selected)
  path (derived from filename), crumb_index, href_* (canonical/index/trailingSlash),
  node_* (virtual/basename/path), plus source provenance.

Notes
  • Does not traverse children/payloads; reads only top-level keys of each array item.
  • Filtered indexes below speed up discovery and JSON scans.
------------------------------------------------------------------------------*/
DROP VIEW IF EXISTS spry_route_crumb;
CREATE VIEW spry_route_crumb AS
WITH files AS (
  SELECT
    f.path          AS src_path,
    f.last_modified AS src_last_modified,
    f.contents,
    -- filename-derived logical path (strip prefix/suffix)
    substr(
      f.path,
      length(''spry.d/auto/breadcrumbs/'') + 1,
      length(f.path) - length(''spry.d/auto/breadcrumbs/'') - length(''.auto.json'')
    ) AS path_spf_target
  FROM sqlpage_files AS f
  WHERE f.path GLOB ''spry.d/auto/breadcrumbs/**/*.auto.json''
    AND json_valid(f.contents)
),
-- One row per crumb (array element)
raw AS (
  SELECT
    files.src_path,
    files.src_last_modified,
    files.path_spf_target,                     -- qualified to avoid json_each.path name
    CAST(c.key AS INTEGER) AS crumb_index,
    c.value                AS crumb
  FROM files
  JOIN json_each(files.contents) AS c
)
SELECT
  -- logical path derived from the filename
  path_spf_target AS path_spf,
  CASE
    WHEN substr(path_spf_target,1,1)=''/'' THEN path_spf_target
    ELSE ''/'' || path_spf_target
  END                               AS path_href,
  crumb_index,

  -- hrefs.* (top-level)
  crumb ->> ''$.hrefs.canonical''     AS href_canonical,
  crumb ->> ''$.hrefs.index''         AS href_index,
  crumb ->> ''$.hrefs.trailingSlash'' AS href_trailing_slash,

  -- node.* (top-level)
  crumb ->> ''$.node.virtual''  AS node_virtual,
  crumb ->> ''$.node.basename'' AS node_basename,
  crumb ->> ''$.node.path''     AS node_path,

  -- provenance
  src_path            AS breadcrumbs_source_path,
  src_last_modified   AS breadcrumbs_source_last_modified
FROM raw
WHERE json_type(crumb, ''$.node.path'') = ''text''
ORDER BY src_path, crumb_index;

-- Breadcrumb discovery + JSON scan accelerators
CREATE INDEX IF NOT EXISTS idx_breadcrumbs_dir
  ON sqlpage_files (path)
  WHERE path GLOB ''spry.d/auto/breadcrumbs/**/*.auto.json'';

CREATE INDEX IF NOT EXISTS idx_breadcrumbs_json
  ON sqlpage_files (json_extract(contents))
  WHERE path GLOB ''spry.d/auto/breadcrumbs/**/*.auto.json'';

-- ---------------------------------------------------------------------------
-- View: spry_route_edge
-- Purpose: Flatten prebuilt edges (parent → child) into rows, newest-wins.
-- Inputs:  sqlpage_files where path GLOB ''spry.d/auto/route/*edges*.auto.json''
-- Columns: parent, child, edges_source_path, edges_source_last_modified
-- ---------------------------------------------------------------------------
DROP VIEW IF EXISTS spry_route_edge;
CREATE VIEW spry_route_edge AS
WITH files AS (
  SELECT
    f.path          AS src_path,
    f.last_modified AS src_last_modified,
    f.contents
  FROM sqlpage_files AS f
  WHERE f.path GLOB ''spry.d/auto/route/*edges*.auto.json''
    AND json_valid(f.contents)
),
edges_raw AS (
  SELECT
    src_path,
    src_last_modified,
    json_extract(e.value, ''$.parent'') AS parent_path_spf,
    json_extract(e.value, ''$.child'')  AS child_path_spf
  FROM files, json_each(files.contents) AS e
  WHERE json_type(e.value, ''$.parent'') = ''text''
    AND json_type(e.value, ''$.child'')  = ''text''
),
ranked AS (
  SELECT
    parent_path_spf,
    child_path_spf,
    src_path,
    src_last_modified,
    ROW_NUMBER() OVER (
      PARTITION BY parent_path_spf, child_path_spf
      ORDER BY src_last_modified DESC, src_path DESC
    ) AS rn
  FROM edges_raw
)
SELECT
  parent_path_spf,
  child_path_spf,
  CASE
    WHEN substr(parent_path_spf,1,1)=''/'' THEN parent_path_spf
    ELSE ''/'' || parent_path_spf
  END               AS parent_path_href,
  CASE
    WHEN substr(child_path_spf,1,1)=''/'' THEN child_path_spf
    ELSE ''/'' || child_path_spf
  END               AS child_path_href,
  src_path          AS edges_source_path,
  src_last_modified AS edges_source_last_modified
FROM ranked
WHERE rn = 1;

-- Edges discovery + JSON scan accelerators on base table
CREATE INDEX IF NOT EXISTS idx_route_edges_dir
  ON sqlpage_files (path)
  WHERE path GLOB ''spry.d/auto/route/*edges*.auto.json'';

CREATE INDEX IF NOT EXISTS idx_route_edges_json
  ON sqlpage_files (json_extract(contents))
  WHERE path GLOB ''spry.d/auto/route/*edges*.auto.json'';

-- ---------------------------------------------------------------------------
-- View: spry_route_child
-- Purpose: Join edges to spry_route for child metadata.
-- Outputs: parent, child, c.* (all columns from spry_route for the child),
--          edges_source_path, edges_source_last_modified
-- ---------------------------------------------------------------------------
DROP VIEW IF EXISTS spry_route_child;
CREATE VIEW spry_route_child AS
SELECT
  e.parent_path_spf,
  e.parent_path_href,
  c.*,
  e.edges_source_path,
  e.edges_source_last_modified
FROM spry_route_edge AS e
JOIN spry_route     AS c
  ON c."path_spf" = e.child_path_spf
ORDER BY e.parent_path_spf, c."path_spf";

-- Speed extraction of the raw ".source" annotations in entry files
CREATE INDEX IF NOT EXISTS idx_entry_source_json
  ON sqlpage_files (json_extract(contents, ''$.".source"''))
  WHERE path GLOB ''spry.d/auto/entry/**/*.auto.json''
    AND json_valid(contents);

-- Speed extraction of the raw ".source" annotations in route files
CREATE INDEX IF NOT EXISTS idx_route_source_json
  ON sqlpage_files (json_extract(contents, ''$.".source"''))
  WHERE path GLOB ''spry.d/auto/route/**/*.auto.json''
    AND json_valid(contents);

-- ---------------------------------------------------------------------------
-- View: spry_entry
-- Purpose: Surface entry metadata + annotations for files under spry.d/auto/entry/**
-- Columns:
--   path           ← contents.webPath
--   nature         ← contents.nature
--   rel_fs_path    ← contents.relFsPath
--   entry_source_path, entry_source_last_modified (provenance)
-- ---------------------------------------------------------------------------
DROP VIEW IF EXISTS spry_entry;
CREATE VIEW spry_entry AS
WITH files AS (
  SELECT
    f.path          AS src_path,
    f.last_modified AS src_last_modified,
    f.contents,
    substr(
      f.path,
      length(''spry.d/auto/entry/'') + 1,
      length(f.path) - length(''spry.d/auto/entry/'') - length(''.auto.json'')
    ) AS path_spf_target
  FROM sqlpage_files AS f
  WHERE f.path GLOB ''spry.d/auto/entry/**/*.auto.json''
    AND json_valid(f.contents)
)
SELECT
  files.contents ->> ''$.webPath''   AS path_spf,
  CASE
    WHEN substr(path_spf_target,1,1)=''/'' THEN path_spf_target
    ELSE ''/'' || path_spf_target
  END                              AS path_href,
  files.contents ->> ''$.nature''    AS nature,
  files.contents ->> ''$.relFsPath'' AS rel_fs_path,
  files.src_path            AS entry_source_path,
  files.src_last_modified   AS entry_source_last_modified
FROM files
WHERE json_type(files.contents, ''$.webPath'') = ''text'';

-- Fast lookups by relFsPath
CREATE INDEX IF NOT EXISTS idx_entry_relfs
  ON sqlpage_files (json_extract(contents, ''$.webPath''))
  WHERE path GLOB ''spry.d/auto/entry/**/*.auto.json'';

-- @spry.nature sql @spry.sqlImpact dml

-- Spry schema information is stored in sqlpage_files as a single-row-per-schema
-- catalog with path ''spry.d/info-schema.auto.json'' (where ''main'' is schhema).
-- Stores a prettifiedJSON graph of the entire SQLite schema (tables, columns, 
-- indexes, foreign keys, views, triggers, and derived relations). These views 
-- project that JSON back into relational form for easy querying. Filter by 
-- schema_name in WHERE clauses (e.g., WHERE s.path = ''spry.d/info-schema.auto.json''). 
-- Requires SQLite JSON1.

-- Populate with a comprehensive JSON graph of the current schema
INSERT OR REPLACE INTO sqlpage_files (path, contents)
VALUES (
  ''spry.d/info-schema.auto.json'',
  json_pretty(
    json_object(
      ''schema_name'', ''main'',
      ''generated_on'', datetime(''now''),
      ''sqlite_version'', sqlite_version(),

      -- Attached databases
      ''databases'',
      (SELECT json_group_array(json_object(''seq'', seq, ''db_name'', name, ''db_file'', file))
         FROM pragma_database_list),

      -- Available collations
      ''collations'',
      (SELECT json_group_array(json_object(''seq'', seq, ''name'', name))
         FROM pragma_collation_list),

      -- Tables keyed by name, with nested columns, indexes, FKs, triggers
      ''tables'',
      (
        SELECT json_group_object(
                 tl.name,
                 json_object(
                   ''type'', tl.type,                 -- ''table''
                   ''strict'', tl.strict,
                   ''without_rowid'', 0,              -- not exposed by pragma_table_list; set 0 by default
                   ''ncol'', tl.ncol,
                   ''sql'', (SELECT s.sql FROM sqlite_schema AS s WHERE s.type=''table'' AND s.name=tl.name),

                   ''columns'',
                   (SELECT json_group_array(
                             json_object(
                               ''cid'', x.cid,
                               ''name'', x.name,
                               ''type'', x.type,
                               ''notnull'', x."notnull",
                               ''dflt_value'', x.dflt_value,
                               ''pk'', x.pk,
                               ''hidden'', x.hidden
                             )
                           )
                      FROM pragma_table_xinfo(tl.name) AS x),

                   ''indexes'',
                   (SELECT json_group_array(
                             json_object(
                               ''name'', il.name,
                               ''origin'', il.origin,          -- ''c'',''u'',''pk''
                               ''unique'', il."unique",
                               ''partial'', il.partial,
                               ''where'',
                                 (SELECT s.sql FROM sqlite_schema AS s
                                   WHERE s.type=''index'' AND s.name=il.name),
                               ''columns'',
                                 (SELECT json_group_array(
                                           json_object(
                                             ''seqno'', ixi.seqno,
                                             ''cid'', ixi.cid,
                                             ''name'', ixi.name,
                                             ''desc'', ixi."desc",
                                             ''coll'', ixi.coll,
                                             ''key'', ixi."key"
                                           )
                                         )
                                    FROM pragma_index_xinfo(il.name) AS ixi)
                             )
                           )
                      FROM pragma_index_list(tl.name) AS il),

                   ''foreign_keys'',
                   (SELECT json_group_array(
                             json_object(
                               ''id'', fk.id,
                               ''seq'', fk.seq,
                               ''from'', fk."from",
                               ''to'', fk."to",
                               ''table'', fk."table",
                               ''on_update'', fk.on_update,
                               ''on_delete'', fk.on_delete,
                               ''match'', fk."match"
                             )
                           )
                      FROM pragma_foreign_key_list(tl.name) AS fk),

                   ''triggers'',
                   (SELECT json_group_array(
                             json_object(
                               ''name'', t.name,
                               ''sql'',  t.sql
                             )
                           )
                      FROM sqlite_schema AS t
                     WHERE t.type=''trigger'' AND t.tbl_name=tl.name)
                 )
               )
          FROM pragma_table_list AS tl
         WHERE tl.type=''table'' AND tl.name NOT LIKE ''sqlite_%''
      ),

      -- Views keyed by name
      ''views'',
      (
        SELECT json_group_object(
                 v.name,
                 json_object(
                   ''type'', ''view'',
                   ''sql'',  v.sql,
                   ''dependencies'', json(''[]'') -- placeholder (dependency parsing is non-trivial)
                 )
               )
          FROM sqlite_schema AS v
         WHERE v.type=''view'' AND v.name NOT LIKE ''sqlite_%''
      ),

      -- Virtual tables keyed by name (basic capture)
      ''virtual_tables'',
      (
        SELECT json_group_object(
                 tl.name,
                 json_object(
                   ''type'', tl.type,  -- ''virtual''
                   ''sql'', (SELECT s.sql FROM sqlite_schema AS s WHERE s.type=''table'' AND s.name=tl.name)
                 )
               )
          FROM pragma_table_list AS tl
         WHERE tl.type=''virtual'' AND tl.name NOT LIKE ''sqlite_%''
      ),

      -- Triggers keyed by name (top-level convenience)
      ''triggers'',
      (
        SELECT json_group_object(
                 t.name,
                 json_object(
                   ''table'', t.tbl_name,
                   ''sql'',   t.sql
                 )
               )
          FROM sqlite_schema AS t
         WHERE t.type=''trigger'' AND t.name NOT LIKE ''sqlite_%''
      ),

      -- Relations derived from all foreign keys
      ''relations'',
      (
        SELECT json_group_array(
                 json_object(
                   ''name'', printf(''%s_%s_%s_%s'', fk.tbl_name, fk."from", fk."table", fk."to"),
                   ''from_table'', fk.tbl_name,
                   ''from_columns'', json_array(fk."from"),
                   ''to_table'', fk."table",
                   ''to_columns'', json_array(fk."to"),
                   ''type'', ''many_to_one'',
                   ''on_update'', fk.on_update,
                   ''on_delete'', fk.on_delete,
                   ''match'', fk."match"
                 )
               )
          FROM (
                 SELECT
                   tbl.name AS tbl_name,
                   fk."from",
                   fk."to",
                   fk."table",
                   fk.on_update,
                   fk.on_delete,
                   fk."match"
                 FROM sqlite_schema AS tbl,
                      pragma_foreign_key_list(tbl.name) AS fk
                 WHERE tbl.type=''table'' AND tbl.name NOT LIKE ''sqlite_%''
               ) AS fk
      )
    )
  )
);

-- Tables (one row per table)
DROP VIEW IF EXISTS spry_schema_info_table;
CREATE VIEW IF NOT EXISTS spry_schema_info_table AS
SELECT
  ''main''                                    AS schema_name,
  t.key                                     AS table_name,
  json_extract(t.value,''$.type'')            AS type,
  json_extract(t.value,''$.ncol'')            AS ncol,
  json_extract(t.value,''$.strict'')          AS strict,
  json_extract(t.value,''$.without_rowid'')   AS without_rowid,
  json_extract(t.value,''$.sql'')             AS definition_sql
FROM sqlpage_files AS s,
     json_each(s.contents, ''$.tables'') AS t
WHERE s.path = ''spry.d/info-schema.auto.json'';

-- Table columns (one row per column per table)
DROP VIEW IF EXISTS spry_schema_info_table_column;
CREATE VIEW IF NOT EXISTS spry_schema_info_table_column AS
SELECT
  ''main''                                        AS schema_name,
  t.key                                         AS table_name,
  json_extract(c.value,''$.cid'')                 AS cid,
  json_extract(c.value,''$.name'')                AS column_name,
  json_extract(c.value,''$.type'')                AS column_type,
  json_extract(c.value,''$.notnull'')             AS not_null,
  json_extract(c.value,''$.dflt_value'')          AS dflt_value,
  json_extract(c.value,''$.pk'')                  AS part_of_pk,
  json_extract(c.value,''$.hidden'')              AS hidden
FROM sqlpage_files AS s,
     json_each(s.contents, ''$.tables'') AS t,
     json_each(t.value, ''$.columns'')           AS c
WHERE s.path = ''spry.d/info-schema.auto.json'';

-- Views (one row per view)
DROP VIEW IF EXISTS spry_schema_info_view;
CREATE VIEW IF NOT EXISTS spry_schema_info_view AS
SELECT
  ''main''                           AS schema_name,
  v.key                            AS view_name,
  json_extract(v.value,''$.type'')   AS type,
  json_extract(v.value,''$.sql'')    AS definition_sql
FROM sqlpage_files AS s,
     json_each(s.contents, ''$.views'') AS v
WHERE s.path = ''spry.d/info-schema.auto.json'';

-- View columns (if your schema_graph_json includes a $.views[*].columns array)
DROP VIEW IF EXISTS spry_schema_info_view_column;
CREATE VIEW IF NOT EXISTS spry_schema_info_view_column AS
SELECT
  ''main''                          AS schema_name,
  v.key                           AS view_name,
  json_extract(vc.value,''$.cid'')  AS cid,
  json_extract(vc.value,''$.name'') AS column_name,
  json_extract(vc.value,''$.type'') AS column_type,
  json_extract(vc.value,''$.notnull'') AS not_null,
  json_extract(vc.value,''$.dflt_value'') AS dflt_value
FROM sqlpage_files AS s,
     json_each(s.contents, ''$.views'') AS v
LEFT JOIN json_each(v.value, ''$.columns'') AS vc ON 1=1
WHERE s.path = ''spry.d/info-schema.auto.json'';

-- Indexes (one row per index per table)
DROP VIEW IF EXISTS spry_schema_info_index;
CREATE VIEW IF NOT EXISTS spry_schema_info_index AS
SELECT
  ''main''                                       AS schema_name,
  t.key                                        AS table_name,
  json_extract(i.value,''$.name'')               AS index_name,
  json_extract(i.value,''$.origin'')             AS origin,      -- ''c'',''u'',''pk''
  json_extract(i.value,''$.unique'')             AS is_unique,
  json_extract(i.value,''$.partial'')            AS is_partial,
  json_extract(i.value,''$.where'')              AS definition_sql
FROM sqlpage_files AS s,
     json_each(s.contents, ''$.tables'')         AS t,
     json_each(t.value, ''$.indexes'')           AS i
WHERE s.path = ''spry.d/info-schema.auto.json'';

-- Index columns (one row per column per index)
DROP VIEW IF EXISTS spry_schema_info_index_column;
CREATE VIEW IF NOT EXISTS spry_schema_info_index_column AS
SELECT
  ''main''                                        AS schema_name,
  t.key                                         AS table_name,
  json_extract(i.value,''$.name'')                AS index_name,
  json_extract(ic.value,''$.seqno'')              AS seqno,
  json_extract(ic.value,''$.cid'')                AS cid,
  json_extract(ic.value,''$.name'')               AS column_name,
  json_extract(ic.value,''$.desc'')               AS is_desc,
  json_extract(ic.value,''$.coll'')               AS collation_name,
  json_extract(ic.value,''$.key'')                AS is_key_column
FROM sqlpage_files AS s,
     json_each(s.contents, ''$.tables'')          AS t,
     json_each(t.value, ''$.indexes'')            AS i,
     json_each(i.value, ''$.columns'')            AS ic
WHERE s.path = ''spry.d/info-schema.auto.json'';

-- Foreign keys (one row per referencing column)
DROP VIEW IF EXISTS spry_schema_info_foreign_key;
CREATE VIEW IF NOT EXISTS spry_schema_info_foreign_key AS
SELECT
  ''main''                                   AS schema_name,
  t.key                                    AS table_name,
  json_extract(fk.value,''$.id'')            AS fk_id,
  json_extract(fk.value,''$.seq'')           AS seq,
  json_extract(fk.value,''$.from'')          AS from_column,
  json_extract(fk.value,''$.to'')            AS to_column,
  json_extract(fk.value,''$.table'')         AS ref_table,
  json_extract(fk.value,''$.on_update'')     AS on_update,
  json_extract(fk.value,''$.on_delete'')     AS on_delete,
  json_extract(fk.value,''$.match'')         AS match
FROM sqlpage_files AS s,
     json_each(s.contents, ''$.tables'')     AS t,
     json_each(t.value, ''$.foreign_keys'')  AS fk
WHERE s.path = ''spry.d/info-schema.auto.json'';

-- Table triggers (one row per trigger per table)
DROP VIEW IF EXISTS spry_schema_info_table_trigger;
CREATE VIEW IF NOT EXISTS spry_schema_info_table_trigger AS
SELECT
  ''main''                                AS schema_name,
  t.key                                 AS table_name,
  json_extract(tr.value,''$.name'')       AS trigger_name,
  json_extract(tr.value,''$.sql'')        AS definition_sql
FROM sqlpage_files AS s,
     json_each(s.contents, ''$.tables'')  AS t,
     json_each(t.value, ''$.triggers'')   AS tr
WHERE s.path = ''spry.d/info-schema.auto.json'';

-- Top-level triggers (if captured under $.triggers object)
DROP VIEW IF EXISTS spry_schema_info_trigger;
CREATE VIEW IF NOT EXISTS spry_schema_info_trigger AS
SELECT
  ''main''                                AS schema_name,
  trg.key                               AS trigger_name,
  json_extract(trg.value,''$.table'')     AS table_name,
  json_extract(trg.value,''$.sql'')       AS definition_sql
FROM sqlpage_files AS s,
     json_each(s.contents, ''$.triggers'') AS trg
WHERE s.path = ''spry.d/info-schema.auto.json'';

-- Relations derived in schema_graph_json (one row per relation)
DROP VIEW IF EXISTS spry_schema_info_relation;
CREATE VIEW IF NOT EXISTS spry_schema_info_relation AS
SELECT
  ''main''                                  AS schema_name,
  json_extract(r.value,''$.name'')          AS relation_name,
  json_extract(r.value,''$.from_table'')    AS from_table,
  json_extract(r.value,''$.to_table'')      AS to_table,
  json_extract(r.value,''$.type'')          AS relation_type,
  json_extract(r.value,''$.on_update'')     AS on_update,
  json_extract(r.value,''$.on_delete'')     AS on_delete,
  json_extract(r.value,''$.match'')         AS match,
  json_extract(r.value,''$.from_columns'')  AS from_columns_json,
  json_extract(r.value,''$.to_columns'')    AS to_columns_json
FROM sqlpage_files AS s,
     json_each(s.contents, ''$.relations'') AS r
WHERE s.path = ''spry.d/info-schema.auto.json'';

-- head SQL defined in `DeploySQL.headSQL` (file:///home/<USER>/workspaces/spry/lib/engine/orchestrate.ts:509:46) (end)
-- sqlpage_files rows --
delete from "sqlpage_files" where "sqlpage_files"."path" = ''deno.json'';
insert into "sqlpage_files" ("path", "contents", "last_modified") values (''deno.json'', ''{
  "imports": {
    "@libsql/client": "npm:@libsql/client@^0.15.14",
    "drizzle-kit": "npm:drizzle-kit@^0.31.4",
    "drizzle-orm": "npm:drizzle-orm@^0.44.5"
  },
  "tasks": {
    "check": "deno check ../../../lib/engine"
  }
}'', CURRENT_TIMESTAMP);
delete from "sqlpage_files" where "sqlpage_files"."path" = ''sqlpage-package.sql'';
', CURRENT_TIMESTAMP);
delete from "sqlpage_files" where "sqlpage_files"."path" = 'sqlpage/sqlpage.json';
insert into "sqlpage_files" ("path", "contents", "last_modified") values ('sqlpage/sqlpage.json', '{
  "allow_exec": true,
  "port": 9219,
  "database_url": "sqlite://sqlpage.db?mode=rwc",
  "web_root": "src"
}', CURRENT_TIMESTAMP);
delete from "sqlpage_files" where "sqlpage_files"."path" = 'index.sql';
insert into "sqlpage_files" ("path", "contents", "last_modified") values ('index.sql', '-- @route.title ''Application'' @route.caption "App Home"
-- @route.description ''Welcome to Spry Application'' 
SELECT ''dynamic'' AS component, sqlpage.run_sql(''spry/shell.sql'') AS properties;

SELECT ''text'' AS component, sqlpage.path() as contents;

SELECT ''list'' AS component;
SELECT caption as title, COALESCE(url, path_href) as link, description
  FROM spry_route
 WHERE path_href = sqlpage.path();

select 
    ''text'' as component,
    ''This is a default primary end-to-end (`e2e-prime`) landing page, replace `index.sql` to add your content.'' as contents_md;
', CURRENT_TIMESTAMP);
delete from "sqlpage_files" where "sqlpage_files"."path" = 'spry/console/action/populate-table-content.sql';
insert into "sqlpage_files" ("path", "contents", "last_modified") values ('spry/console/action/populate-table-content.sql', '-- @spry.nature action
SELECT ''dynamic'' AS component, sqlpage.run_sql(''spry/console/lib/populate-table-content.sql'') AS properties;

SELECT ''redirect'' AS component, COALESCE(sqlpage.environment_variable(''SQLPAGE_SITE_PREFIX''), '''') || ''/spry/console/sqlpage-files/content.sql'' as link WHERE $redirect is NULL;
SELECT ''redirect'' AS component, COALESCE(sqlpage.environment_variable(''SQLPAGE_SITE_PREFIX''), '''') || $redirect as link WHERE $redirect is NOT NULL;', CURRENT_TIMESTAMP);
delete from "sqlpage_files" where "sqlpage_files"."path" = 'spry/console/action/populate-spry-table-info.sql';
insert into "sqlpage_files" ("path", "contents", "last_modified") values ('spry/console/action/populate-spry-table-info.sql', '-- @spry.nature action
SELECT ''dynamic'' AS component, sqlpage.run_sql(''spry/console/lib/populate-spry-table-info.sql'') AS properties;

SELECT ''redirect'' AS component, COALESCE(sqlpage.environment_variable(''SQLPAGE_SITE_PREFIX''), '''') || ''/spry/console/info-schema/index.sql'' as link WHERE $redirect is NULL;
SELECT ''redirect'' AS component, COALESCE(sqlpage.environment_variable(''SQLPAGE_SITE_PREFIX''), '''') || $redirect as link WHERE $redirect is NOT NULL;', CURRENT_TIMESTAMP);
delete from "sqlpage_files" where "sqlpage_files"."path" = 'spry/console/sqlpage-nav/index.sql';
insert into "sqlpage_files" ("path", "contents", "last_modified") values ('spry/console/sqlpage-nav/index.sql', '-- @route.title ''Spry Navigation Routes'' @route.caption "Spry Routes"
-- @route.description ''Spry Backend-as-a-Service (BaaS) Navigation Routes'' 
SELECT ''dynamic'' AS component, sqlpage.run_sql(''spry/shell.sql'') AS properties;
SELECT ''dynamic'' AS component, sqlpage.run_sql(''spry/nav-breadcrumbs.sql'') AS properties;

SELECT ''title'' AS component, ''Spry BaaS navigation in spry_route table'' AS contents;
SELECT ''table'' AS component, TRUE as sort, TRUE as search;
SELECT * FROM spry_route ORDER BY path, sibling_order;
', CURRENT_TIMESTAMP);
delete from "sqlpage_files" where "sqlpage_files"."path" = 'spry/console/sqlpage-files/sqlpage-file.sql';
insert into "sqlpage_files" ("path", "contents", "last_modified") values ('spry/console/sqlpage-files/sqlpage-file.sql', 'SELECT ''dynamic'' AS component, sqlpage.run_sql(''spry/shell.sql'') AS properties;
SELECT ''dynamic'' AS component, sqlpage.run_sql(''spry/nav-breadcrumbs.sql'') AS properties;

SELECT $path || '' Path'' AS title, ''#'' AS link;

      SELECT ''title'' AS component, $path AS contents;
      SELECT ''text'' AS component,
             ''```sql
'' || (select sqlpage.read_file_as_text(''spry.d/auto/entry/entries.auto.json'') as contents) || ''
```'' as contents_md;
            ', CURRENT_TIMESTAMP);
delete from "sqlpage_files" where "sqlpage_files"."path" = 'spry/console/sqlpage-files/index.sql';
insert into "sqlpage_files" ("path", "contents", "last_modified") values ('spry/console/sqlpage-files/index.sql', '-- @route.title ''SQLPage Files Table'' @route.caption "SQLPage Files"
SELECT ''dynamic'' AS component, sqlpage.run_sql(''spry/shell.sql'') AS properties;
SELECT ''dynamic'' AS component, sqlpage.run_sql(''spry/nav-breadcrumbs.sql'') AS properties;          

SELECT ''title'' AS component, ''SQLPage pages in sqlpage_files table'' AS contents;
SELECT ''table'' AS component,
      ''Path'' as markdown,
      ''Size'' as align_right,
      TRUE as sort,
      TRUE as search;
   SELECT
  ''[🚀]('' || COALESCE(sqlpage.environment_variable(''SQLPAGE_SITE_PREFIX''), '''') || ''/'' || path || '') [📄 '' || path || ''](sqlpage-file.sql?path='' || path || '')'' AS "Path",
  nature, LENGTH(contents) as "Size", last_modified
FROM sqlpage_files
ORDER BY path;            ', CURRENT_TIMESTAMP);
delete from "sqlpage_files" where "sqlpage_files"."path" = 'spry/console/sqlpage-files/content.sql';
insert into "sqlpage_files" ("path", "contents", "last_modified") values ('spry/console/sqlpage-files/content.sql', '-- @route.title ''SQLPage Content'' @route.caption "SQLPage Files Content"
-- @route.description Manage and regenerate sqlpage_files content in database
SELECT ''dynamic'' AS component, sqlpage.run_sql(''spry/shell.sql'') AS properties;
SELECT ''dynamic'' AS component, sqlpage.run_sql(''spry/nav-breadcrumbs.sql'') AS properties;           

SELECT ''title'' AS component, ''SQLPage pages generated from tables and views'' AS contents;
SELECT ''text'' AS component, ''
  - `*.auto.sql` pages are auto-generated "default" content pages for each table and view defined in the database.
  - The `*.sql` companions may be auto-generated redirects to their `*.auto.sql` pair or an app/service might override the `*.sql` to not redirect and supply custom content for any table or view.
  - [View populate-table-content.sql]('' || COALESCE(sqlpage.environment_variable(''SQLPAGE_SITE_PREFIX''), '''') || ''/spry/console/sqlpage-files/sqlpage-file.sql?path=/spry/console/action/populate-table-content.sql'' || '')
  '' AS contents_md;

SELECT ''button'' AS component, ''center'' AS justify;
SELECT COALESCE(sqlpage.environment_variable(''SQLPAGE_SITE_PREFIX''), '''') || ''/spry/console/action/populate-table-content.sql'' AS link, ''info'' AS color, ''Regenerate all "default" table/view content pages'' AS title;

SELECT ''title'' AS component, ''Redirected or overriden content pages'' as contents;
SELECT ''table'' AS component,
      ''Path'' as markdown,
      ''Size'' as align_right,
      TRUE as sort,
      TRUE as search;
      SELECT
  ''[🚀]('' || COALESCE(sqlpage.environment_variable(''SQLPAGE_SITE_PREFIX''), '''') || ''/'' || path || '')[📄 '' || path || ''](sqlpage-file.sql?path='' || path || '')'' AS "Path",

  LENGTH(contents) as "Size", last_modified
FROM sqlpage_files
WHERE path like ''spry/console/content/%''
      AND NOT(path like ''spry/console/content/%.auto.sql'')
      AND NOT(path like ''spry/console/content/action%'')
ORDER BY path;

SELECT ''title'' AS component, ''Auto-generated "default" content pages'' as contents;
SELECT ''table'' AS component,
      ''Path'' as markdown,
      ''Size'' as align_right,
      TRUE as sort,
      TRUE as search;
    SELECT
      ''[🚀]('' || COALESCE(sqlpage.environment_variable(''SQLPAGE_SITE_PREFIX''), '''') || ''/'' || path || '') [📄 '' || path || ''](sqlpage-file.sql?path='' || path || '')'' AS "Path",

  LENGTH(contents) as "Size", last_modified
FROM sqlpage_files
WHERE path like ''spry/console/content/%.auto.sql''
ORDER BY path;            ', CURRENT_TIMESTAMP);
delete from "sqlpage_files" where "sqlpage_files"."path" = 'spry/console/index.sql';
insert into "sqlpage_files" ("path", "contents", "last_modified") values ('spry/console/index.sql', '-- @route.title ''Spry BaaS Console'' @route.caption "Spry Console"
-- @route.description ''Spry Backend-as-a-Service (BaaS) Console'' 
SELECT ''dynamic'' AS component, sqlpage.run_sql(''spry/shell.sql'') AS properties;
SELECT ''dynamic'' AS component, sqlpage.run_sql(''spry/nav-breadcrumbs.sql'') AS properties;

WITH console_navigation_cte AS (
    SELECT title, description
      FROM spry_route
     WHERE path_href = sqlpage.path()
)
SELECT ''list'' AS component, title || '' '' || sqlpage.path() as title, description
  FROM console_navigation_cte;
SELECT caption as title, COALESCE(sqlpage.environment_variable(''SQLPAGE_SITE_PREFIX''), '''') || COALESCE(url, path_href) as link, description
  FROM spry_route_child
 WHERE parent_path_href = sqlpage.path()
 ORDER BY sibling_order;
            ', CURRENT_TIMESTAMP);
delete from "sqlpage_files" where "sqlpage_files"."path" = 'spry/console/lib/populate-table-content.sql';
insert into "sqlpage_files" ("path", "contents", "last_modified") values ('spry/console/lib/populate-table-content.sql', '-- @spry.nature sql @spry.sqlImpact dml

-- TODO: explain how this file is used to generate "default" or auto-generated content for all tables
--       it''s especially useful to see how to generate API endpoints, etc. automatically

-- the "auto-generated" tables will be in ''*.auto.sql'' with redirects
DELETE FROM sqlpage_files WHERE path like ''spry/console/content/table/%.auto.sql'';
DELETE FROM sqlpage_files WHERE path like ''spry/console/content/view/%.auto.sql'';
INSERT OR REPLACE INTO sqlpage_files (path, contents)
SELECT
    ''spry/console/content/'' || tabular_nature || ''/'' || tabular_name || ''.auto.sql'',
    ''SELECT ''''dynamic'''' AS component, sqlpage.run_sql(''''spry/shell.sql'''') AS properties;

        SELECT ''''breadcrumb'''' AS component;
        SELECT ''''Home'''' as title, COALESCE(sqlpage.environment_variable(''''SQLPAGE_SITE_PREFIX''''), '''''''''''') || ''''/spry/index.sql'''' AS link;
        SELECT ''''Console'''' as title, COALESCE(sqlpage.environment_variable(''''SQLPAGE_SITE_PREFIX''''), '''''''''''') || ''''/spry/console/index.sql'''' AS link;
        SELECT ''''Content'''' as title, COALESCE(sqlpage.environment_variable(''''SQLPAGE_SITE_PREFIX''''), '''''''''''') || ''''/spry/console/content/index.sql'''' AS link;
        SELECT '''''' || tabular_name  || '' '' || tabular_nature || '''''' as title, ''''#'''' AS link;

        SELECT ''''title'''' AS component, '''''' || tabular_name || '' ('' || tabular_nature || '') Content'''' as contents;

        SET total_rows = (SELECT COUNT(*) FROM '' || tabular_name || '');
        SET limit = COALESCE($limit, 50);
        SET offset = COALESCE($offset, 0);
        SET total_pages = ($total_rows + $limit - 1) / $limit;
        SET current_page = ($offset / $limit) + 1;

        SELECT ''''text'''' AS component, '''''' || info_schema_link_full_md || '''''' AS contents_md
        SELECT ''''text'''' AS component,
        ''''- Start Row: '''' || $offset || ''''
'''' ||
        ''''- Rows per Page: '''' || $limit || ''''
'''' ||
        ''''- Total Rows: '''' || $total_rows || ''''
'''' ||
        ''''- Current Page: '''' || $current_page || ''''
'''' ||
        ''''- Total Pages: '''' || $total_pages as contents_md
        WHERE $stats IS NOT NULL;

        -- Display uniform_resource table with pagination
        SELECT ''''table'''' AS component,
            TRUE AS sort,
            TRUE AS search,
            TRUE AS hover,
            TRUE AS striped_rows,
            TRUE AS small;
    SELECT * FROM '' || tabular_name || ''
    LIMIT $limit
    OFFSET $offset;

    SELECT ''''text'''' AS component,
        (SELECT CASE WHEN $current_page > 1 THEN ''''[Previous](?limit='''' || $limit || ''''&offset='''' || ($offset - $limit) || '''')'''' ELSE '''' '''' END) || '''' '''' ||
        ''''(Page '''' || $current_page || '''' of '''' || $total_pages || '''') '''' ||
        (SELECT CASE WHEN $current_page < $total_pages THEN ''''[Next](?limit='''' || $limit || ''''&offset='''' || ($offset + $limit) || '''')'''' ELSE '''' '''' END)
        AS contents_md;''
FROM spry_console_content_tabular;

-- if there are no overrides, create some defaults
-- `INSERT OR IGNORE` is used so that if custom pages exist, we don''t touch them
INSERT OR IGNORE INTO sqlpage_files (path, contents)
SELECT
    ''spry/console/content/'' || tabular_nature || ''/'' || tabular_name || ''.sql'',
    ''SELECT ''''redirect'''' AS component, COALESCE(sqlpage.environment_variable(''''SQLPAGE_SITE_PREFIX''''), '''''''''''') || ''''/spry/console/content/'' || tabular_nature || ''/'' || tabular_name || ''.auto.sql'''' AS link WHERE $stats IS NULL;
'' ||
    ''SELECT ''''redirect'''' AS component, COALESCE(sqlpage.environment_variable(''''SQLPAGE_SITE_PREFIX''''), '''''''''''') || ''''/spry/console/content/'' || tabular_nature || ''/'' || tabular_name || ''.auto.sql?stats='''' || $stats AS link WHERE $stats IS NOT NULL;''
FROM spry_console_content_tabular;
', CURRENT_TIMESTAMP);
delete from "sqlpage_files" where "sqlpage_files"."path" = 'spry/console/lib/console-info-schema.ddl.sql';
insert into "sqlpage_files" ("path", "contents", "last_modified") values ('spry/console/lib/console-info-schema.ddl.sql', '-- console_information_schema_* are convenience views
-- built on top of spry_table_info (filled via pragma_table_xinfo).

-- -----------------------------------------------------------------------------
-- spry_console_info_schema_table
-- -----------------------------------------------------------------------------
-- Purpose:
--   Presents an "information schema"-style catalog for *tables* only, built on
--   top of the materialized metadata helper table `spry_table_info`.
--   We use `spry_table_info` because SQLite views cannot call table-valued
--   PRAGMAs (e.g., pragma_table_info / pragma_table_xinfo) directly.
--
-- What it shows (one row per table column):
--   - table_name     : user table name from sqlite_schema
--   - column_name    : column name for that table
--   - data_type      : declared column type
--   - is_primary_key : whether the column participates in the PK (pk > 0)
--   - is_not_null    : whether the column has a NOT NULL constraint
--   - default_value  : column default expression/value (if any)
--   - several convenience web-UI paths / Markdown links for your console
--   - sql_ddl        : the table''s original CREATE TABLE DDL
--
-- Notes:
--   - Filters to real user tables (type=''table'' and name NOT LIKE ''sqlite_%'').
--   - Filters out hidden columns that appear in xinfo (generated columns, rowid
--     aliases, etc.), i.e. IFNULL(col.hidden, 0) = 0.
--   - Orders rows by table and column id (cid) so columns appear in DDL order.

DROP VIEW IF EXISTS spry_console_info_schema_table;
CREATE VIEW spry_console_info_schema_table AS
SELECT tbl.name AS table_name,
       col.name AS column_name,
       col.type AS data_type,
       CASE WHEN col.pk > 0 THEN ''Yes'' ELSE ''No'' END AS is_primary_key,
       CASE WHEN col."notnull" = 1 THEN ''Yes'' ELSE ''No'' END AS is_not_null,
       col.dflt_value AS default_value,
       ''console/info-schema/table.sql?name='' || tbl.name || ''&stats=yes'' as info_schema_web_ui_path,
       ''[Content](console/info-schema/table.sql?name='' || tbl.name || ''&stats=yes)'' as info_schema_link_abbrev_md,
       ''['' || tbl.name || '' (table) Schema](console/info-schema/table.sql?name='' || tbl.name || ''&stats=yes)'' as info_schema_link_full_md,
       ''console/content/table/'' || tbl.name || ''.sql?stats=yes'' as content_web_ui_path,
       ''[Content]($SITE_PREFIX_URL/spry/console/content/table/'' || tbl.name || ''.sql?stats=yes)'' as content_web_ui_link_abbrev_md,
       ''['' || tbl.name || '' (table) Content]($SITE_PREFIX_URL/spry/console/content/table/'' || tbl.name || ''.sql?stats=yes)'' as content_web_ui_link_full_md,
       tbl.sql as sql_ddl
FROM sqlite_schema AS tbl
JOIN spry_table_info AS col
ON col.table_name = tbl.name
WHERE tbl.type = ''table''
      AND tbl.name NOT LIKE ''sqlite_%''
      AND IFNULL(col.hidden, 0) = 0          -- show only visible columns from xinfo
ORDER BY tbl.name, col.cid;

-- -----------------------------------------------------------------------------
-- spry_console_info_schema_view
-- -----------------------------------------------------------------------------
-- Purpose:
--   Presents an "information schema"-style catalog for *views* only, again
--   built on `spry_table_info` so we do not invoke PRAGMAs inside this view.
--
-- What it shows (one row per view column):
--   - view_name      : user view name from sqlite_schema
--   - column_name    : column name as inferred by xinfo (via materialization)
--   - data_type      : declared/propagated type if available
--   - various console web-UI paths and Markdown links for convenience
--   - sql_ddl        : the view''s original CREATE VIEW text
--
-- Notes:
--   - Filters to real user views (type=''view'' and name NOT LIKE ''sqlite_%'').
--   - Filters out hidden xinfo columns, if any.
--   - Orders by view name and column id (cid).

DROP VIEW IF EXISTS spry_console_info_schema_view;
CREATE VIEW spry_console_info_schema_view AS
SELECT vw.name AS view_name,
       col.name AS column_name,
       col.type AS data_type,
       ''/console/info-schema/view.sql?name='' || vw.name || ''&stats=yes'' as info_schema_web_ui_path,
       ''[Content](console/info-schema/view.sql?name='' || vw.name || ''&stats=yes)'' as info_schema_link_abbrev_md,
       ''['' || vw.name || '' (view) Schema](console/info-schema/view.sql?name='' || vw.name || ''&stats=yes)'' as info_schema_link_full_md,
       ''/console/content/view/'' || vw.name || ''.sql?stats=yes'' as content_web_ui_path,
       ''[Content]($SITE_PREFIX_URL/spry/console/content/view/'' || vw.name || ''.sql?stats=yes)'' as content_web_ui_link_abbrev_md,
       ''['' || vw.name || '' (view) Content]($SITE_PREFIX_URL/spry/console/content/view/'' || vw.name || ''.sql?stats=yes)'' as content_web_ui_link_full_md,
       vw.sql as sql_ddl
FROM sqlite_schema AS vw
JOIN spry_table_info AS col
ON col.table_name = vw.name
WHERE vw.type = ''view''
      AND vw.name NOT LIKE ''sqlite_%''
      AND IFNULL(col.hidden, 0) = 0
ORDER BY vw.name, col.cid;

-- -----------------------------------------------------------------------------
-- spry_console_content_tabular
-- -----------------------------------------------------------------------------
-- Purpose:
--   Unifies the "table" and "view" information-schema rows into a single,
--   tabular catalog for navigation/UI consumption. Each row identifies whether
--   it represents a table or a view and carries the same set of link fields so
--   downstream code can render a single list.
--
-- Output columns:
--   - tabular_nature       : ''table'' or ''view''
--   - tabular_name         : the table or view name
--   - info_schema_*        : console info-schema links (path + Markdown variants)
--   - content_web_ui_*     : console content links (path + Markdown variants)
--
-- Notes:
--   - Uses UNION ALL (not UNION) to retain all rows without de-duplication.

DROP VIEW IF EXISTS spry_console_content_tabular;
CREATE VIEW spry_console_content_tabular AS
SELECT ''table'' as tabular_nature,
        table_name as tabular_name,
        info_schema_web_ui_path,
        info_schema_link_abbrev_md,
        info_schema_link_full_md,
        content_web_ui_path,
        content_web_ui_link_abbrev_md,
        content_web_ui_link_full_md
    FROM spry_console_info_schema_table
UNION ALL
SELECT ''view'' as tabular_nature,
        view_name as tabular_name,
        info_schema_web_ui_path,
        info_schema_link_abbrev_md,
        info_schema_link_full_md,
        content_web_ui_path,
        content_web_ui_link_abbrev_md,
        content_web_ui_link_full_md
    FROM spry_console_info_schema_view;

-- -----------------------------------------------------------------------------
-- spry_console_info_schema_table_col_fkey
-- -----------------------------------------------------------------------------
-- Purpose:
--   Lists table *column-level* foreign key relationships in a concise form,
--   one row per FK column, e.g. "order.customer_id references customers.id".
--
-- Output columns:
--   - table_name : the child (referencing) table
--   - column_name: the child column that participates in the FK
--   - foreign_key: a human-readable "from → referenced" string
--
-- Implementation details:
--   - Joins user tables from sqlite_master to the table-valued
--     pragma_foreign_key_list(tbl.name) to fetch FK definitions.
--   - Filters out SQLite internal tables (name NOT LIKE ''sqlite_%'').
--
-- IMPORTANT:
--   Many SQLite builds disallow table-valued PRAGMAs inside views
--   (for safety/nondeterminism reasons). If your environment blocks this,
--   you should *materialize* the FK info into a helper table (similar to
--   spry_table_info) and have this view read from that table instead.

DROP VIEW IF EXISTS spry_console_info_schema_table_col_fkey;
CREATE VIEW spry_console_info_schema_table_col_fkey AS
SELECT
    tbl.name AS table_name,
    f."from" AS column_name,
    f."from" || '' references '' || f."table" || ''.'' || f."to" AS foreign_key
FROM sqlite_master tbl
JOIN pragma_foreign_key_list(tbl.name) f
WHERE tbl.type = ''table'' AND tbl.name NOT LIKE ''sqlite_%'';

-- -----------------------------------------------------------------------------
-- spry_console_info_schema_table_col_index
-- -----------------------------------------------------------------------------
-- Purpose:
--   Lists *per-column index participation* for user tables, one row per
--   (table, column, index) triple.
--
-- Output columns:
--   - table_name : the indexed table
--   - column_name: the column covered by the index (in index position order)
--   - index_name : the index that includes the column
--
-- Implementation details:
--   - For each user table, pulls the list of indexes via pragma_index_list.
--   - For each index, expands its columns via pragma_index_info.
--   - Excludes internal SQLite objects.
--
-- IMPORTANT:
--   As with other table-valued PRAGMAs, some environments forbid using
--   pragma_index_list / pragma_index_info directly in views. If that applies
--   to you, stage the output in a materialized helper table and point this
--   view at that table instead.

DROP VIEW IF EXISTS spry_console_info_schema_table_col_index;
CREATE VIEW spry_console_info_schema_table_col_index AS
SELECT
    tbl.name AS table_name,
    pi.name AS column_name,
    idx.name AS index_name
FROM sqlite_master tbl
JOIN pragma_index_list(tbl.name) idx
JOIN pragma_index_info(idx.name) pi
WHERE tbl.type = ''table'' AND tbl.name NOT LIKE ''sqlite_%''; ', CURRENT_TIMESTAMP);
delete from "sqlpage_files" where "sqlpage_files"."path" = 'spry/console/lib/populate-spry-table-info.sql';
insert into "sqlpage_files" ("path", "contents", "last_modified") values ('spry/console/lib/populate-spry-table-info.sql', '-- @spry.nature sql @spry.sqlImpact ddl

-- REFRESH STEP (run as a separate statement, not inside the view):
-- Rebuild the snapshot so it reflects the current schema at the moment of run.
-- This is necessary because we cannot select pragma_table_xinfo() on-the-fly in views.
DELETE FROM spry_table_info;

-- Repopulate the snapshot from the current schema.
INSERT INTO spry_table_info
SELECT tbl.name AS table_name,
       x.*,
       datetime(''now'') AS generated_on
FROM sqlite_schema AS tbl,
     pragma_table_xinfo(tbl.name) AS x;
', CURRENT_TIMESTAMP);
delete from "sqlpage_files" where "sqlpage_files"."path" = 'spry/console/info-schema/index.sql';
insert into "sqlpage_files" ("path", "contents", "last_modified") values ('spry/console/info-schema/index.sql', '-- @route.title "Spry BaaS Info Schema" @route.caption Spry Schema
SELECT ''dynamic'' AS component, sqlpage.run_sql(''spry/shell.sql'') AS properties;
SELECT ''dynamic'' AS component, sqlpage.run_sql(''spry/nav-breadcrumbs.sql'') AS properties;

select 
    ''text'' as component,
    ''**NOTE**: the content here depends on `spry_table_info` table which _manually_ does what `pragma_table_info` does. '' ||
    ''We cannot use `pragma_table_info` because it is not allowed in SQLite views. '' ||
    ''Regenerate `spry_table_info` if something does not look right. Last generated: '' || (SELECT oldest_row_age from spry_table_info_gen_stats) || '' ago.'' as contents_md;

SELECT ''button'' AS component, ''center'' AS justify;
SELECT COALESCE(sqlpage.environment_variable(''SQLPAGE_SITE_PREFIX''), '''') || ''/spry/console/action/populate-spry-table-info.sql'' AS link, ''info'' AS color, ''Rebuild spry_table_info table'' AS title;

SELECT ''title'' AS component, ''Tables'' as contents;
SELECT ''table'' AS component,
      ''Table'' AS markdown,
      ''Column Count'' as align_right,
      ''Content'' as markdown,
      TRUE as sort,
      TRUE as search;
SELECT
    ''['' || table_name || ''](table.sql?name='' || table_name || '')'' AS "Table",
    COUNT(column_name) AS "Column Count",
    REPLACE(content_web_ui_link_abbrev_md,''$SITE_PREFIX_URL'', COALESCE(sqlpage.environment_variable(''SQLPAGE_SITE_PREFIX''), '''')) as "Content"
FROM spry_console_info_schema_table
GROUP BY table_name;

SELECT ''title'' AS component, ''Views'' as contents;
SELECT ''table'' AS component,
      ''View'' AS markdown,
      ''Column Count'' as align_right,
      ''Content'' as markdown,
      TRUE as sort,
      TRUE as search;
SELECT
    ''['' || view_name || ''](view.sql?name='' || view_name || '')'' AS "View",
    COUNT(column_name) AS "Column Count",
    REPLACE(content_web_ui_link_abbrev_md,''$SITE_PREFIX_URL'', COALESCE(sqlpage.environment_variable(''SQLPAGE_SITE_PREFIX''), '''')) as "Content"
FROM spry_console_info_schema_view
GROUP BY view_name;', CURRENT_TIMESTAMP);
delete from "sqlpage_files" where "sqlpage_files"."path" = 'spry/console/info-schema/table.sql';
insert into "sqlpage_files" ("path", "contents", "last_modified") values ('spry/console/info-schema/table.sql', '-- expects `name` as query param (used as $name)

SELECT ''dynamic'' AS component, sqlpage.run_sql(''spry/shell.sql'') AS properties;            
SELECT ''dynamic'' AS component, sqlpage.run_sql(''spry/nav-breadcrumbs.sql'') AS properties;

SELECT $name || '' Table'' AS title, ''#'' AS link;

SELECT ''title'' AS component, $name AS contents;
SELECT ''table'' AS component;
SELECT
    column_name AS "Column",
    data_type AS "Type",
    is_primary_key AS "PK",
    is_not_null AS "Required",
    default_value AS "Default"
FROM spry_console_info_schema_table
WHERE table_name = $name;

SELECT ''title'' AS component, ''Foreign Keys'' as contents, 2 as level;
SELECT ''table'' AS component;
SELECT
    column_name AS "Column Name",
    foreign_key AS "Foreign Key"
FROM spry_console_info_schema_table_col_fkey
WHERE table_name = $name;

SELECT ''title'' AS component, ''Indexes'' as contents, 2 as level;
SELECT ''table'' AS component;
SELECT
    column_name AS "Column Name",
    index_name AS "Index Name"
FROM spry_console_info_schema_table_col_index
WHERE table_name = $name;

SELECT ''title'' AS component, ''SQL DDL'' as contents, 2 as level;
SELECT ''code'' AS component;
SELECT ''sql'' as language, (SELECT sql_ddl FROM spry_console_info_schema_table WHERE table_name = $name) as contents;
            ', CURRENT_TIMESTAMP);
delete from "sqlpage_files" where "sqlpage_files"."path" = 'spry/console/info-schema/view.sql';
insert into "sqlpage_files" ("path", "contents", "last_modified") values ('spry/console/info-schema/view.sql', '-- expects `name` as query param (used as $name)

SELECT ''dynamic'' AS component, sqlpage.run_sql(''spry/shell.sql'') AS properties;
SELECT ''dynamic'' AS component, sqlpage.run_sql(''spry/nav-breadcrumbs.sql'') AS properties;

SELECT $name || '' View'' AS title, ''#'' AS link;

SELECT ''title'' AS component, $name AS contents;
SELECT ''table'' AS component;
SELECT
    column_name AS "Column",
    data_type AS "Type"
FROM spry_console_info_schema_view
WHERE view_name = $name;

SELECT ''title'' AS component, ''SQL DDL'' as contents, 2 as level;
SELECT ''code'' AS component;
SELECT ''sql'' as language, (SELECT sql_ddl FROM spry_console_info_schema_view WHERE view_name = $name) as contents;
', CURRENT_TIMESTAMP);
delete from "sqlpage_files" where "sqlpage_files"."path" = 'spry/console/about.sql';
insert into "sqlpage_files" ("path", "contents", "last_modified") values ('spry/console/about.sql', '-- @route.title ''About Spry BaaS Console'' @route.caption "About Spry Console"
-- @route.description ''About Spry Backend-as-a-Service (BaaS) Console'' 
SELECT ''dynamic'' AS component, sqlpage.run_sql(''spry/shell.sql'') AS properties;            
SELECT ''dynamic'' AS component, sqlpage.run_sql(''spry/nav-breadcrumbs.sql'') AS properties;
              
SELECT ''text'' AS component, (''Resource Surveillance v'' || replace(sqlpage.exec(''surveilr'', ''--version''), ''surveilr '', '''')) AS title;

SELECT
    ''text'' AS component,
    ''A detailed description of what is incorporated into surveilr. It informs of critical dependencies like rusqlite, sqlpage, pgwire, e.t.c, ensuring they are present and meet version requirements. Additionally, it scans for and executes capturable executables in the PATH and evaluates surveilr_doctor_* database views for more insights.''
    AS contents_md;

-- Section: Dependencies
SELECT
    ''title'' AS component,
    ''Internal Dependencies'' AS contents,
    2 AS level;
SELECT
    ''table'' AS component,
    TRUE AS sort;
SELECT
    "Dependency",
    "Version"
FROM (
    SELECT
        ''SQLPage'' AS "Dependency",
        json_extract(json_data, ''$.versions.sqlpage'') AS "Version"
    FROM (SELECT sqlpage.exec(''surveilr'', ''doctor'', ''--json'') AS json_data)
    UNION ALL
    SELECT
        ''Pgwire'',
        json_extract(json_data, ''$.versions.pgwire'')
    FROM (SELECT sqlpage.exec(''surveilr'', ''doctor'', ''--json'') AS json_data)
    UNION ALL
    SELECT
        ''Rusqlite'',
        json_extract(json_data, ''$.versions.rusqlite'')
    FROM (SELECT sqlpage.exec(''surveilr'', ''doctor'', ''--json'') AS json_data)
);

-- Section: Static Extensions
SELECT
    ''title'' AS component,
    ''Statically Linked Extensions'' AS contents,
    2 AS level;
SELECT
    ''table'' AS component,
    TRUE AS sort;
SELECT
    json_extract(value, ''$.name'') AS "Extension Name",
    json_extract(value, ''$.url'') AS "URL",
    json_extract(value, ''$.version'') AS "Version"
FROM json_each(
    json_extract(sqlpage.exec(''surveilr'', ''doctor'', ''--json''), ''$.static_extensions'')
);

-- Section: Dynamic Extensions
SELECT
    ''title'' AS component,
    ''Dynamically Linked Extensions'' AS contents,
    2 AS level;
SELECT
    ''table'' AS component,
    TRUE AS sort;
SELECT
    json_extract(value, ''$.name'') AS "Extension Name",
    json_extract(value, ''$.path'') AS "Path"
FROM json_each(
    json_extract(sqlpage.exec(''surveilr'', ''doctor'', ''--json''), ''$.dynamic_extensions'')
);

-- Section: Environment Variables
SELECT
    ''title'' AS component,
    ''Environment Variables'' AS contents,
    2 AS level;
SELECT
    ''table'' AS component,
    TRUE AS sort;
SELECT
    json_extract(value, ''$.name'') AS "Variable",
    json_extract(value, ''$.value'') AS "Value"
FROM json_each(
    json_extract(sqlpage.exec(''surveilr'', ''doctor'', ''--json''), ''$.env_vars'')
);

-- Section: Capturable Executables
SELECT
    ''title'' AS component,
    ''Capturable Executables'' AS contents,
    2 AS level;
SELECT
    ''table'' AS component,
    TRUE AS sort;
SELECT
    json_extract(value, ''$.name'') AS "Executable Name",
    json_extract(value, ''$.output'') AS "Output"
FROM json_each(
    json_extract(sqlpage.exec(''surveilr'', ''doctor'', ''--json''), ''$.capturable_executables'')
);

SELECT ''title'' AS component, ''Views'' as contents;
SELECT ''table'' AS component,
    ''View'' AS markdown,
    ''Column Count'' as align_right,
    ''Content'' as markdown,
    TRUE as sort,
    TRUE as search;

SELECT ''['' || view_name || ''](/console/info-schema/view.sql?name='' || view_name || '')'' AS "View",
COUNT(column_name) AS "Column Count",
REPLACE(content_web_ui_link_abbrev_md, ''$SITE_PREFIX_URL'', COALESCE(sqlpage.environment_variable(''SQLPAGE_SITE_PREFIX''), '''')) AS "Content"
FROM spry_console_info_schema_view
WHERE view_name LIKE ''surveilr_doctor%''
GROUP BY view_name;
        ', CURRENT_TIMESTAMP);
delete from "sqlpage_files" where "sqlpage_files"."path" = 'spry/index.sql';
insert into "sqlpage_files" ("path", "contents", "last_modified") values ('spry/index.sql', '-- #include blockName -f ./middleware/file.sql
/** this will be replaced! **/
-- #includeEnd blockName

-- @route.title ''Spry Backend-as-a-Service (BaaS)'' @route.caption "Spry BaaS"
-- @route.description ''Welcome to Spry Backend-as-a-Service (BaaS) Database'' 
SELECT ''dynamic'' AS component, sqlpage.run_sql(''spry/shell.sql'') AS properties;

SELECT
   ''title'' AS component,
   ''Entries annotated with @spry.* or @route.* tags in comments'' AS contents,
   1 AS level;

SET entries_json = sqlpage.read_file_as_text(''spry.d/auto/entry/entries.auto.json'');
SET entries_json_safe = COALESCE(NULLIF($entries_json, ''''), ''[]'');

SELECT ''big_number'' as component;
WITH items AS (
  SELECT j.value AS item
  FROM (SELECT $entries_json_safe AS entries_json)
  CROSS JOIN json_each(entries_json) AS j
)
SELECT
  COALESCE(item ->> ''$.nature'', ''(none)'') AS title,
  COUNT(*) AS value
FROM items
GROUP BY title
ORDER BY title, title;

SELECT
  ''table'' AS component,
  ''Entries'' AS title,
  true AS striped_rows;

WITH json_src AS (
  SELECT j.value AS item
  FROM (SELECT $entries_json AS entries_json)
  CROSS JOIN json_each(COALESCE(NULLIF(entries_json, ''''), ''[]'')) AS j
)
SELECT
  item ->> ''$.isSystemGenerated''   AS "Sys?",
  item ->> ''$.nature''              AS "Nature",
  item ->> ''$.route.caption''       AS "Route Caption",
  item ->> ''$.webPath''             AS "SQLPage Path",
  item ->> ''$.relFsPath''           AS "Project Path"
FROM json_src
ORDER BY "Nature";
', CURRENT_TIMESTAMP);
delete from "sqlpage_files" where "sqlpage_files"."path" = 'spry/lib/sqlpage-files.ddl.sql';
insert into "sqlpage_files" ("path", "contents", "last_modified") values ('spry/lib/sqlpage-files.ddl.sql', '-- @spry.nature sql @spry.sqlImpact ddl

-- See https://github.com/sqlpage/SQLPage#hosting-sql-files-directly-inside-the-database
-- TODO: generate this using Drizzle Kit

/*------------------------------------------------------------------------------
Table: sqlpage_files

What it stores
  One row per “file” that SQLPage/Spry can read:
    • SQL pages / partials (as text)
    • JSON control files (routes, breadcrumbs, entries, etc.)

Why it exists
  Keeps runtime-ready artifacts inside the DB so routing, nav, and content can be
  queried and transformed with SQL.

How it’s used
  • SQLPage: maps path → contents to render pages.
  • Spry: reads JSON files under spry.d/auto/* for routes/breadcrumbs/entries, etc.

Columns
  path          PK “filename” (e.g. spry/console/index.sql.auto.json)
  contents      The raw text/JSON for the file
  last_modified For cache-invalidation and “pick newest” semantics
------------------------------------------------------------------------------*/
CREATE TABLE IF NOT EXISTS "sqlpage_files" (
  "path" VARCHAR PRIMARY KEY NOT NULL,
  "contents" TEXT NOT NULL,
  "last_modified" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Helpful general index for recency-based joins/filters and debugging
CREATE INDEX IF NOT EXISTS idx_sqlpage_files_last_modified
  ON sqlpage_files (last_modified);

-- ---------------------------------------------------------------------------

-- ---------------------------------------------------------------------------
-- View: spry_annotation
-- Purpose:
--   Flatten annotations from `.source` objects found in:
--     - spry.d/auto/route/**/*.auto.json   (path = $.path)
--     - spry.d/auto/entry/**/*.auto.json   (path = $.webPath)
--   One row per annotation key (e.g., "title", "caption", "description").
--
-- Columns:
--   path        TEXT   -- logical path for the resource (route.path or entry.webPath)
--   namespace   TEXT   -- ''route'' | ''entry''
--   annotation  TEXT   -- the key inside `.source` (e.g., ''title'')
--   id          TEXT   -- $.id inside the annotation object
--   key         TEXT   -- $.key (e.g., ''route.title'')
--   kind        TEXT   -- $.kind (e.g., ''tag'')
--   value       TEXT   -- $.value (human string)
--   raw         TEXT   -- $.raw (original tag text)
--   source      JSON   -- $.source (raw JSON with languageId, loc, etc.)
-- ---------------------------------------------------------------------------
DROP VIEW IF EXISTS spry_annotation;
CREATE VIEW spry_annotation AS
WITH route_files AS (
  SELECT
    contents,
    contents ->> ''$.path'' AS path
  FROM sqlpage_files
  WHERE path GLOB ''spry.d/auto/route/**/*.auto.json''
    AND json_valid(contents)
    AND json_type(contents, ''$.path'') = ''text''
    AND json_type(contents, ''$.".source"'') = ''object''
),
entry_files AS (
  SELECT
    contents,
    contents ->> ''$.webPath'' AS path
  FROM sqlpage_files
  WHERE path GLOB ''spry.d/auto/entry/**/*.auto.json''
    AND json_valid(contents)
    AND json_type(contents, ''$.webPath'') = ''text''
    AND json_type(contents, ''$.".source"'') = ''object''
),
route_ann AS (
  SELECT
    rf.path                         AS path_spf,
    CASE
      WHEN substr(rf.path,1,1)=''/'' THEN rf.path
      ELSE ''/'' || rf.path
    END                             AS path_href,
    ''route''                         AS namespace,
    a.key                           AS annotation,
    a.value ->> ''$.id''              AS id,
    a.value ->> ''$.key''             AS key,
    a.value ->> ''$.kind''            AS kind,
    a.value ->> ''$.value''           AS value,
    a.value ->> ''$.raw''             AS raw,
    a.value ->  ''$.source''          AS source
  FROM route_files rf,
       json_each(rf.contents, ''$.".source"'') AS a
),
entry_ann AS (
  SELECT
    ef.path                         AS path,
    CASE
      WHEN substr(ef.path,1,1)=''/'' THEN ef.path
      ELSE ''/'' || ef.path
    END                             AS path_href,
    ''entry''                         AS namespace,
    a.key                           AS annotation,
    a.value ->> ''$.id''              AS id,
    a.value ->> ''$.key''             AS key,
    a.value ->> ''$.kind''            AS kind,
    a.value ->> ''$.value''           AS value,
    a.value ->> ''$.raw''             AS raw,
    a.value ->  ''$.source''          AS source
  FROM entry_files ef,
       json_each(ef.contents, ''$.".source"'') AS a
)
SELECT * FROM route_ann
UNION ALL
SELECT * FROM entry_ann;

CREATE INDEX IF NOT EXISTS idx_route_source_json
  ON sqlpage_files (json_extract(contents, ''$.".source"''))
  WHERE path GLOB ''spry.d/auto/route/**/*.auto.json'' AND json_valid(contents);

CREATE INDEX IF NOT EXISTS idx_entry_source_json
  ON sqlpage_files (json_extract(contents, ''$.".source"''))
  WHERE path GLOB ''spry.d/auto/entry/**/*.auto.json'' AND json_valid(contents);

CREATE INDEX IF NOT EXISTS idx_route_path
  ON sqlpage_files (contents ->> ''$.path'')
  WHERE path GLOB ''spry.d/auto/route/**/*.auto.json'';

CREATE INDEX IF NOT EXISTS idx_entry_webpath
  ON sqlpage_files (contents ->> ''$.webPath'')
  WHERE path GLOB ''spry.d/auto/entry/**/*.auto.json'';

/*------------------------------------------------------------------------------
View: spry_route

Purpose
  Projects one row per route from files under spry.d/auto/route/**\/*.auto.json.

Shape (selected)
  path, title, caption, url, description, elaboration, plus provenance:
  spf_path (source file path), spf_last_modified.

Notes
  • Expects each file’s contents to be a JSON object with the route fields.
  • Filtered indexes below speed up discovery and path lookups.
------------------------------------------------------------------------------*/
DROP VIEW IF EXISTS spry_route;
CREATE VIEW spry_route AS
WITH f AS (
  SELECT
    path          AS spf_path,
    last_modified AS spf_last_modified,
    contents,
    substr(
      path,
      length(''spry.d/auto/route/'') + 1,
      length(path) - length(''spry.d/auto/route/'') - length(''.auto.json'')
    )             AS path_spf_target
  FROM sqlpage_files
  WHERE path GLOB ''spry.d/auto/route/**/*.auto.json''
    AND json_valid(contents)
)
SELECT
  contents ->> ''$.path''                AS "path_spf",
  path_spf_target                      AS "path_spf_target",
  CASE
    WHEN substr(path_spf_target,1,1)=''/'' THEN path_spf_target
    ELSE ''/'' || path_spf_target
  END                                  AS path_href,  
  contents ->> ''$.pathBasename''        AS "path_basename",
  contents ->> ''$.pathBasenameNoExtn''  AS "path_basename_no_extn",
  contents ->> ''$.pathDirname''         AS "path_dirname",
  contents ->> ''$.pathExtnTerminal''    AS "path_extn_terminal",
  contents ->  ''$.pathExtns''           AS "path_extns",

  contents ->> ''$.caption''             AS "caption",
  contents ->> ''$.siblingOrder''        AS "sibling_order",
  contents ->> ''$.url''                 AS "url",
  contents ->> ''$.title''               AS "title",
  contents ->> ''$.abbreviatedCaption''  AS "abbreviated_caption",
  contents ->> ''$.description''         AS "description",
  contents ->  ''$.elaboration''         AS "elaboration",

  spf_path,
  spf_last_modified
FROM f
WHERE json_type(contents, ''$.path'') = ''text'';

-- fast lookups by route path
CREATE INDEX IF NOT EXISTS idx_route_json_path_flat
  ON sqlpage_files (contents ->> ''$.path'')
  WHERE path GLOB ''spry.d/auto/route/**/*.auto.json'';

-- help queries that scan JSON content
CREATE INDEX IF NOT EXISTS idx_route_json_scan
  ON sqlpage_files (json(contents))  -- or (contents -> ''$'') if you prefer
  WHERE path GLOB ''spry.d/auto/route/**/*.auto.json'';

/*------------------------------------------------------------------------------
View: spry_route_crumb

Purpose
  Emits one row per breadcrumb “crumb” from files under
  spry.d/auto/breadcrumbs/**\/*.auto.json (each file is an array of objects).

Shape (selected)
  path (derived from filename), crumb_index, href_* (canonical/index/trailingSlash),
  node_* (virtual/basename/path), plus source provenance.

Notes
  • Does not traverse children/payloads; reads only top-level keys of each array item.
  • Filtered indexes below speed up discovery and JSON scans.
------------------------------------------------------------------------------*/
DROP VIEW IF EXISTS spry_route_crumb;
CREATE VIEW spry_route_crumb AS
WITH files AS (
  SELECT
    f.path          AS src_path,
    f.last_modified AS src_last_modified,
    f.contents,
    -- filename-derived logical path (strip prefix/suffix)
    substr(
      f.path,
      length(''spry.d/auto/breadcrumbs/'') + 1,
      length(f.path) - length(''spry.d/auto/breadcrumbs/'') - length(''.auto.json'')
    ) AS path_spf_target
  FROM sqlpage_files AS f
  WHERE f.path GLOB ''spry.d/auto/breadcrumbs/**/*.auto.json''
    AND json_valid(f.contents)
),
-- One row per crumb (array element)
raw AS (
  SELECT
    files.src_path,
    files.src_last_modified,
    files.path_spf_target,                     -- qualified to avoid json_each.path name
    CAST(c.key AS INTEGER) AS crumb_index,
    c.value                AS crumb
  FROM files
  JOIN json_each(files.contents) AS c
)
SELECT
  -- logical path derived from the filename
  path_spf_target AS path_spf,
  CASE
    WHEN substr(path_spf_target,1,1)=''/'' THEN path_spf_target
    ELSE ''/'' || path_spf_target
  END                               AS path_href,
  crumb_index,

  -- hrefs.* (top-level)
  crumb ->> ''$.hrefs.canonical''     AS href_canonical,
  crumb ->> ''$.hrefs.index''         AS href_index,
  crumb ->> ''$.hrefs.trailingSlash'' AS href_trailing_slash,

  -- node.* (top-level)
  crumb ->> ''$.node.virtual''  AS node_virtual,
  crumb ->> ''$.node.basename'' AS node_basename,
  crumb ->> ''$.node.path''     AS node_path,

  -- provenance
  src_path            AS breadcrumbs_source_path,
  src_last_modified   AS breadcrumbs_source_last_modified
FROM raw
WHERE json_type(crumb, ''$.node.path'') = ''text''
ORDER BY src_path, crumb_index;

-- Breadcrumb discovery + JSON scan accelerators
CREATE INDEX IF NOT EXISTS idx_breadcrumbs_dir
  ON sqlpage_files (path)
  WHERE path GLOB ''spry.d/auto/breadcrumbs/**/*.auto.json'';

CREATE INDEX IF NOT EXISTS idx_breadcrumbs_json
  ON sqlpage_files (json_extract(contents))
  WHERE path GLOB ''spry.d/auto/breadcrumbs/**/*.auto.json'';

-- ---------------------------------------------------------------------------
-- View: spry_route_edge
-- Purpose: Flatten prebuilt edges (parent → child) into rows, newest-wins.
-- Inputs:  sqlpage_files where path GLOB ''spry.d/auto/route/*edges*.auto.json''
-- Columns: parent, child, edges_source_path, edges_source_last_modified
-- ---------------------------------------------------------------------------
DROP VIEW IF EXISTS spry_route_edge;
CREATE VIEW spry_route_edge AS
WITH files AS (
  SELECT
    f.path          AS src_path,
    f.last_modified AS src_last_modified,
    f.contents
  FROM sqlpage_files AS f
  WHERE f.path GLOB ''spry.d/auto/route/*edges*.auto.json''
    AND json_valid(f.contents)
),
edges_raw AS (
  SELECT
    src_path,
    src_last_modified,
    json_extract(e.value, ''$.parent'') AS parent_path_spf,
    json_extract(e.value, ''$.child'')  AS child_path_spf
  FROM files, json_each(files.contents) AS e
  WHERE json_type(e.value, ''$.parent'') = ''text''
    AND json_type(e.value, ''$.child'')  = ''text''
),
ranked AS (
  SELECT
    parent_path_spf,
    child_path_spf,
    src_path,
    src_last_modified,
    ROW_NUMBER() OVER (
      PARTITION BY parent_path_spf, child_path_spf
      ORDER BY src_last_modified DESC, src_path DESC
    ) AS rn
  FROM edges_raw
)
SELECT
  parent_path_spf,
  child_path_spf,
  CASE
    WHEN substr(parent_path_spf,1,1)=''/'' THEN parent_path_spf
    ELSE ''/'' || parent_path_spf
  END               AS parent_path_href,
  CASE
    WHEN substr(child_path_spf,1,1)=''/'' THEN child_path_spf
    ELSE ''/'' || child_path_spf
  END               AS child_path_href,
  src_path          AS edges_source_path,
  src_last_modified AS edges_source_last_modified
FROM ranked
WHERE rn = 1;

-- Edges discovery + JSON scan accelerators on base table
CREATE INDEX IF NOT EXISTS idx_route_edges_dir
  ON sqlpage_files (path)
  WHERE path GLOB ''spry.d/auto/route/*edges*.auto.json'';

CREATE INDEX IF NOT EXISTS idx_route_edges_json
  ON sqlpage_files (json_extract(contents))
  WHERE path GLOB ''spry.d/auto/route/*edges*.auto.json'';

-- ---------------------------------------------------------------------------
-- View: spry_route_child
-- Purpose: Join edges to spry_route for child metadata.
-- Outputs: parent, child, c.* (all columns from spry_route for the child),
--          edges_source_path, edges_source_last_modified
-- ---------------------------------------------------------------------------
DROP VIEW IF EXISTS spry_route_child;
CREATE VIEW spry_route_child AS
SELECT
  e.parent_path_spf,
  e.parent_path_href,
  c.*,
  e.edges_source_path,
  e.edges_source_last_modified
FROM spry_route_edge AS e
JOIN spry_route     AS c
  ON c."path_spf" = e.child_path_spf
ORDER BY e.parent_path_spf, c."path_spf";

-- Speed extraction of the raw ".source" annotations in entry files
CREATE INDEX IF NOT EXISTS idx_entry_source_json
  ON sqlpage_files (json_extract(contents, ''$.".source"''))
  WHERE path GLOB ''spry.d/auto/entry/**/*.auto.json''
    AND json_valid(contents);

-- Speed extraction of the raw ".source" annotations in route files
CREATE INDEX IF NOT EXISTS idx_route_source_json
  ON sqlpage_files (json_extract(contents, ''$.".source"''))
  WHERE path GLOB ''spry.d/auto/route/**/*.auto.json''
    AND json_valid(contents);

-- ---------------------------------------------------------------------------
-- View: spry_entry
-- Purpose: Surface entry metadata + annotations for files under spry.d/auto/entry/**
-- Columns:
--   path           ← contents.webPath
--   nature         ← contents.nature
--   rel_fs_path    ← contents.relFsPath
--   entry_source_path, entry_source_last_modified (provenance)
-- ---------------------------------------------------------------------------
DROP VIEW IF EXISTS spry_entry;
CREATE VIEW spry_entry AS
WITH files AS (
  SELECT
    f.path          AS src_path,
    f.last_modified AS src_last_modified,
    f.contents,
    substr(
      f.path,
      length(''spry.d/auto/entry/'') + 1,
      length(f.path) - length(''spry.d/auto/entry/'') - length(''.auto.json'')
    ) AS path_spf_target
  FROM sqlpage_files AS f
  WHERE f.path GLOB ''spry.d/auto/entry/**/*.auto.json''
    AND json_valid(f.contents)
)
SELECT
  files.contents ->> ''$.webPath''   AS path_spf,
  CASE
    WHEN substr(path_spf_target,1,1)=''/'' THEN path_spf_target
    ELSE ''/'' || path_spf_target
  END                              AS path_href,
  files.contents ->> ''$.nature''    AS nature,
  files.contents ->> ''$.relFsPath'' AS rel_fs_path,
  files.src_path            AS entry_source_path,
  files.src_last_modified   AS entry_source_last_modified
FROM files
WHERE json_type(files.contents, ''$.webPath'') = ''text'';

-- Fast lookups by relFsPath
CREATE INDEX IF NOT EXISTS idx_entry_relfs
  ON sqlpage_files (json_extract(contents, ''$.webPath''))
  WHERE path GLOB ''spry.d/auto/entry/**/*.auto.json'';
', CURRENT_TIMESTAMP);
delete from "sqlpage_files" where "sqlpage_files"."path" = 'spry/lib/schema-info.dml.sql';
insert into "sqlpage_files" ("path", "contents", "last_modified") values ('spry/lib/schema-info.dml.sql', '-- @spry.nature sql @spry.sqlImpact dml

-- Spry schema information is stored in sqlpage_files as a single-row-per-schema
-- catalog with path ''spry.d/info-schema.auto.json'' (where ''main'' is schhema).
-- Stores a prettifiedJSON graph of the entire SQLite schema (tables, columns, 
-- indexes, foreign keys, views, triggers, and derived relations). These views 
-- project that JSON back into relational form for easy querying. Filter by 
-- schema_name in WHERE clauses (e.g., WHERE s.path = ''spry.d/info-schema.auto.json''). 
-- Requires SQLite JSON1.

-- Populate with a comprehensive JSON graph of the current schema
INSERT OR REPLACE INTO sqlpage_files (path, contents)
VALUES (
  ''spry.d/info-schema.auto.json'',
  json_pretty(
    json_object(
      ''schema_name'', ''main'',
      ''generated_on'', datetime(''now''),
      ''sqlite_version'', sqlite_version(),

      -- Attached databases
      ''databases'',
      (SELECT json_group_array(json_object(''seq'', seq, ''db_name'', name, ''db_file'', file))
         FROM pragma_database_list),

      -- Available collations
      ''collations'',
      (SELECT json_group_array(json_object(''seq'', seq, ''name'', name))
         FROM pragma_collation_list),

      -- Tables keyed by name, with nested columns, indexes, FKs, triggers
      ''tables'',
      (
        SELECT json_group_object(
                 tl.name,
                 json_object(
                   ''type'', tl.type,                 -- ''table''
                   ''strict'', tl.strict,
                   ''without_rowid'', 0,              -- not exposed by pragma_table_list; set 0 by default
                   ''ncol'', tl.ncol,
                   ''sql'', (SELECT s.sql FROM sqlite_schema AS s WHERE s.type=''table'' AND s.name=tl.name),

                   ''columns'',
                   (SELECT json_group_array(
                             json_object(
                               ''cid'', x.cid,
                               ''name'', x.name,
                               ''type'', x.type,
                               ''notnull'', x."notnull",
                               ''dflt_value'', x.dflt_value,
                               ''pk'', x.pk,
                               ''hidden'', x.hidden
                             )
                           )
                      FROM pragma_table_xinfo(tl.name) AS x),

                   ''indexes'',
                   (SELECT json_group_array(
                             json_object(
                               ''name'', il.name,
                               ''origin'', il.origin,          -- ''c'',''u'',''pk''
                               ''unique'', il."unique",
                               ''partial'', il.partial,
                               ''where'',
                                 (SELECT s.sql FROM sqlite_schema AS s
                                   WHERE s.type=''index'' AND s.name=il.name),
                               ''columns'',
                                 (SELECT json_group_array(
                                           json_object(
                                             ''seqno'', ixi.seqno,
                                             ''cid'', ixi.cid,
                                             ''name'', ixi.name,
                                             ''desc'', ixi."desc",
                                             ''coll'', ixi.coll,
                                             ''key'', ixi."key"
                                           )
                                         )
                                    FROM pragma_index_xinfo(il.name) AS ixi)
                             )
                           )
                      FROM pragma_index_list(tl.name) AS il),

                   ''foreign_keys'',
                   (SELECT json_group_array(
                             json_object(
                               ''id'', fk.id,
                               ''seq'', fk.seq,
                               ''from'', fk."from",
                               ''to'', fk."to",
                               ''table'', fk."table",
                               ''on_update'', fk.on_update,
                               ''on_delete'', fk.on_delete,
                               ''match'', fk."match"
                             )
                           )
                      FROM pragma_foreign_key_list(tl.name) AS fk),

                   ''triggers'',
                   (SELECT json_group_array(
                             json_object(
                               ''name'', t.name,
                               ''sql'',  t.sql
                             )
                           )
                      FROM sqlite_schema AS t
                     WHERE t.type=''trigger'' AND t.tbl_name=tl.name)
                 )
               )
          FROM pragma_table_list AS tl
         WHERE tl.type=''table'' AND tl.name NOT LIKE ''sqlite_%''
      ),

      -- Views keyed by name
      ''views'',
      (
        SELECT json_group_object(
                 v.name,
                 json_object(
                   ''type'', ''view'',
                   ''sql'',  v.sql,
                   ''dependencies'', json(''[]'') -- placeholder (dependency parsing is non-trivial)
                 )
               )
          FROM sqlite_schema AS v
         WHERE v.type=''view'' AND v.name NOT LIKE ''sqlite_%''
      ),

      -- Virtual tables keyed by name (basic capture)
      ''virtual_tables'',
      (
        SELECT json_group_object(
                 tl.name,
                 json_object(
                   ''type'', tl.type,  -- ''virtual''
                   ''sql'', (SELECT s.sql FROM sqlite_schema AS s WHERE s.type=''table'' AND s.name=tl.name)
                 )
               )
          FROM pragma_table_list AS tl
         WHERE tl.type=''virtual'' AND tl.name NOT LIKE ''sqlite_%''
      ),

      -- Triggers keyed by name (top-level convenience)
      ''triggers'',
      (
        SELECT json_group_object(
                 t.name,
                 json_object(
                   ''table'', t.tbl_name,
                   ''sql'',   t.sql
                 )
               )
          FROM sqlite_schema AS t
         WHERE t.type=''trigger'' AND t.name NOT LIKE ''sqlite_%''
      ),

      -- Relations derived from all foreign keys
      ''relations'',
      (
        SELECT json_group_array(
                 json_object(
                   ''name'', printf(''%s_%s_%s_%s'', fk.tbl_name, fk."from", fk."table", fk."to"),
                   ''from_table'', fk.tbl_name,
                   ''from_columns'', json_array(fk."from"),
                   ''to_table'', fk."table",
                   ''to_columns'', json_array(fk."to"),
                   ''type'', ''many_to_one'',
                   ''on_update'', fk.on_update,
                   ''on_delete'', fk.on_delete,
                   ''match'', fk."match"
                 )
               )
          FROM (
                 SELECT
                   tbl.name AS tbl_name,
                   fk."from",
                   fk."to",
                   fk."table",
                   fk.on_update,
                   fk.on_delete,
                   fk."match"
                 FROM sqlite_schema AS tbl,
                      pragma_foreign_key_list(tbl.name) AS fk
                 WHERE tbl.type=''table'' AND tbl.name NOT LIKE ''sqlite_%''
               ) AS fk
      )
    )
  )
);

-- Tables (one row per table)
DROP VIEW IF EXISTS spry_schema_info_table;
CREATE VIEW IF NOT EXISTS spry_schema_info_table AS
SELECT
  ''main''                                    AS schema_name,
  t.key                                     AS table_name,
  json_extract(t.value,''$.type'')            AS type,
  json_extract(t.value,''$.ncol'')            AS ncol,
  json_extract(t.value,''$.strict'')          AS strict,
  json_extract(t.value,''$.without_rowid'')   AS without_rowid,
  json_extract(t.value,''$.sql'')             AS definition_sql
FROM sqlpage_files AS s,
     json_each(s.contents, ''$.tables'') AS t
WHERE s.path = ''spry.d/info-schema.auto.json'';

-- Table columns (one row per column per table)
DROP VIEW IF EXISTS spry_schema_info_table_column;
CREATE VIEW IF NOT EXISTS spry_schema_info_table_column AS
SELECT
  ''main''                                        AS schema_name,
  t.key                                         AS table_name,
  json_extract(c.value,''$.cid'')                 AS cid,
  json_extract(c.value,''$.name'')                AS column_name,
  json_extract(c.value,''$.type'')                AS column_type,
  json_extract(c.value,''$.notnull'')             AS not_null,
  json_extract(c.value,''$.dflt_value'')          AS dflt_value,
  json_extract(c.value,''$.pk'')                  AS part_of_pk,
  json_extract(c.value,''$.hidden'')              AS hidden
FROM sqlpage_files AS s,
     json_each(s.contents, ''$.tables'') AS t,
     json_each(t.value, ''$.columns'')           AS c
WHERE s.path = ''spry.d/info-schema.auto.json'';

-- Views (one row per view)
DROP VIEW IF EXISTS spry_schema_info_view;
CREATE VIEW IF NOT EXISTS spry_schema_info_view AS
SELECT
  ''main''                           AS schema_name,
  v.key                            AS view_name,
  json_extract(v.value,''$.type'')   AS type,
  json_extract(v.value,''$.sql'')    AS definition_sql
FROM sqlpage_files AS s,
     json_each(s.contents, ''$.views'') AS v
WHERE s.path = ''spry.d/info-schema.auto.json'';

-- View columns (if your schema_graph_json includes a $.views[*].columns array)
DROP VIEW IF EXISTS spry_schema_info_view_column;
CREATE VIEW IF NOT EXISTS spry_schema_info_view_column AS
SELECT
  ''main''                          AS schema_name,
  v.key                           AS view_name,
  json_extract(vc.value,''$.cid'')  AS cid,
  json_extract(vc.value,''$.name'') AS column_name,
  json_extract(vc.value,''$.type'') AS column_type,
  json_extract(vc.value,''$.notnull'') AS not_null,
  json_extract(vc.value,''$.dflt_value'') AS dflt_value
FROM sqlpage_files AS s,
     json_each(s.contents, ''$.views'') AS v
LEFT JOIN json_each(v.value, ''$.columns'') AS vc ON 1=1
WHERE s.path = ''spry.d/info-schema.auto.json'';

-- Indexes (one row per index per table)
DROP VIEW IF EXISTS spry_schema_info_index;
CREATE VIEW IF NOT EXISTS spry_schema_info_index AS
SELECT
  ''main''                                       AS schema_name,
  t.key                                        AS table_name,
  json_extract(i.value,''$.name'')               AS index_name,
  json_extract(i.value,''$.origin'')             AS origin,      -- ''c'',''u'',''pk''
  json_extract(i.value,''$.unique'')             AS is_unique,
  json_extract(i.value,''$.partial'')            AS is_partial,
  json_extract(i.value,''$.where'')              AS definition_sql
FROM sqlpage_files AS s,
     json_each(s.contents, ''$.tables'')         AS t,
     json_each(t.value, ''$.indexes'')           AS i
WHERE s.path = ''spry.d/info-schema.auto.json'';

-- Index columns (one row per column per index)
DROP VIEW IF EXISTS spry_schema_info_index_column;
CREATE VIEW IF NOT EXISTS spry_schema_info_index_column AS
SELECT
  ''main''                                        AS schema_name,
  t.key                                         AS table_name,
  json_extract(i.value,''$.name'')                AS index_name,
  json_extract(ic.value,''$.seqno'')              AS seqno,
  json_extract(ic.value,''$.cid'')                AS cid,
  json_extract(ic.value,''$.name'')               AS column_name,
  json_extract(ic.value,''$.desc'')               AS is_desc,
  json_extract(ic.value,''$.coll'')               AS collation_name,
  json_extract(ic.value,''$.key'')                AS is_key_column
FROM sqlpage_files AS s,
     json_each(s.contents, ''$.tables'')          AS t,
     json_each(t.value, ''$.indexes'')            AS i,
     json_each(i.value, ''$.columns'')            AS ic
WHERE s.path = ''spry.d/info-schema.auto.json'';

-- Foreign keys (one row per referencing column)
DROP VIEW IF EXISTS spry_schema_info_foreign_key;
CREATE VIEW IF NOT EXISTS spry_schema_info_foreign_key AS
SELECT
  ''main''                                   AS schema_name,
  t.key                                    AS table_name,
  json_extract(fk.value,''$.id'')            AS fk_id,
  json_extract(fk.value,''$.seq'')           AS seq,
  json_extract(fk.value,''$.from'')          AS from_column,
  json_extract(fk.value,''$.to'')            AS to_column,
  json_extract(fk.value,''$.table'')         AS ref_table,
  json_extract(fk.value,''$.on_update'')     AS on_update,
  json_extract(fk.value,''$.on_delete'')     AS on_delete,
  json_extract(fk.value,''$.match'')         AS match
FROM sqlpage_files AS s,
     json_each(s.contents, ''$.tables'')     AS t,
     json_each(t.value, ''$.foreign_keys'')  AS fk
WHERE s.path = ''spry.d/info-schema.auto.json'';

-- Table triggers (one row per trigger per table)
DROP VIEW IF EXISTS spry_schema_info_table_trigger;
CREATE VIEW IF NOT EXISTS spry_schema_info_table_trigger AS
SELECT
  ''main''                                AS schema_name,
  t.key                                 AS table_name,
  json_extract(tr.value,''$.name'')       AS trigger_name,
  json_extract(tr.value,''$.sql'')        AS definition_sql
FROM sqlpage_files AS s,
     json_each(s.contents, ''$.tables'')  AS t,
     json_each(t.value, ''$.triggers'')   AS tr
WHERE s.path = ''spry.d/info-schema.auto.json'';

-- Top-level triggers (if captured under $.triggers object)
DROP VIEW IF EXISTS spry_schema_info_trigger;
CREATE VIEW IF NOT EXISTS spry_schema_info_trigger AS
SELECT
  ''main''                                AS schema_name,
  trg.key                               AS trigger_name,
  json_extract(trg.value,''$.table'')     AS table_name,
  json_extract(trg.value,''$.sql'')       AS definition_sql
FROM sqlpage_files AS s,
     json_each(s.contents, ''$.triggers'') AS trg
WHERE s.path = ''spry.d/info-schema.auto.json'';

-- Relations derived in schema_graph_json (one row per relation)
DROP VIEW IF EXISTS spry_schema_info_relation;
CREATE VIEW IF NOT EXISTS spry_schema_info_relation AS
SELECT
  ''main''                                  AS schema_name,
  json_extract(r.value,''$.name'')          AS relation_name,
  json_extract(r.value,''$.from_table'')    AS from_table,
  json_extract(r.value,''$.to_table'')      AS to_table,
  json_extract(r.value,''$.type'')          AS relation_type,
  json_extract(r.value,''$.on_update'')     AS on_update,
  json_extract(r.value,''$.on_delete'')     AS on_delete,
  json_extract(r.value,''$.match'')         AS match,
  json_extract(r.value,''$.from_columns'')  AS from_columns_json,
  json_extract(r.value,''$.to_columns'')    AS to_columns_json
FROM sqlpage_files AS s,
     json_each(s.contents, ''$.relations'') AS r
WHERE s.path = ''spry.d/info-schema.auto.json'';
', CURRENT_TIMESTAMP);
delete from "sqlpage_files" where "sqlpage_files"."path" = 'spry/nav-breadcrumbs.sql';
insert into "sqlpage_files" ("path", "contents", "last_modified") values ('spry/nav-breadcrumbs.sql', '-- @spry.nature partial
-- to override the path, use sqlpage.run_sql(''spry/nav-breadcrumbs.sql'', ''{ "path": "/xyz" }'')
-- for debugging use sqlpage.run_sql(''spry/nav-breadcrumbs.sql'', ''{ "nature": "table" }'')

SELECT ''text'' AS component, "TODO: implement nav-breadcrumbs.sql" as contents;
SELECT ''text'' AS component, substr(sqlpage.path(), 2) as contents;


-- SELECT CASE WHEN COALESCE($nature, ''breadcrumbs'') = ''table'' THEN ''table'' ELSE ''breadcrumb'' END AS component;
-- SELECT href_canonical as title, COALESCE(sqlpage.environment_variable(''SQLPAGE_SITE_PREFIX''), '''') || href_canonical as link
-- FROM spry_route_crumb 
-- WHERE path_href = sqlpage.path()
-- ORDER BY crumb_index;', CURRENT_TIMESTAMP);
delete from "sqlpage_files" where "sqlpage_files"."path" = 'spry/shell.sql';
insert into "sqlpage_files" ("path", "contents", "last_modified") values ('spry/shell.sql', '-- @spry.nature partial
-- set role = (
--     SELECT role FROM users
--     INNER JOIN sessions ON users.id = sessions.user_id
--     WHERE sessions.session_id = sqlpage.cookie(''session_id'')
-- ); -- Read more about how to handle user sessions in the "authentication" component documentation
-- SELECT 
--     ''shell'' AS component,
--     ''My authenticated website'' AS title,

--     -- Add an admin panel link if the user is an admin
--     CASE WHEN $role = ''admin'' THEN ''{"link": "admin.sql", "title": "Admin panel"}'' END AS menu_item,

--     -- Add a profile page if the user is authenticated
--     CASE WHEN $role IS NOT NULL THEN ''{"link": "profile.sql", "title": "My profile"}'' END AS menu_item,

--     -- Add a login link if the user is not authenticated
--     CASE WHEN $role IS NULL THEN ''login'' END AS menu_item
-- ;

SELECT ''shell'' AS component,
       ''Spry BaaS'' AS title,
       NULL AS icon,
       ''https://www.surveilr.com/assets/brand/favicon.ico'' AS favicon,
       ''https://www.surveilr.com/assets/brand/surveilr-icon.png'' AS image,
       ''fluid'' AS layout,
       true AS fixed_top_menu,
       ''/spry/index.sql'' AS link,
       ''{"link":"/spry/index.sql","title":"Home"}'' AS menu_item,
       ''https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11/build/highlight.min.js'' AS javascript,
       ''https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11/build/languages/sql.min.js'' AS javascript,
       ''https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11/build/languages/handlebars.min.js'' AS javascript,
       ''https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11/build/languages/json.min.js'' AS javascript,
       json_object(
              ''link'', COALESCE(sqlpage.environment_variable(''SQLPAGE_SITE_PREFIX''), '''') || ''/spry/docs/index.sql'',
              ''title'', ''Docs''
            --   ''submenu'', (
            --       SELECT json_group_array(
            --           json_object(
            --               ''title'', title,
            --               ''link'', COALESCE(sqlpage.environment_variable(''SQLPAGE_SITE_PREFIX''), '''') || link,
            --               ''description'', description
            --           )
            --       )
            --       FROM (
            --           SELECT
            --               COALESCE(abbreviated_caption, caption) as title,
            --               COALESCE(url, path_href) as link,
            --               description
            --           FROM spry_route
            --           WHERE path_href = ''/spry/docs''
            --           ORDER BY sibling_order
            --       )
            --   )
          ) as menu_item,       
       json_object(
              ''link'', COALESCE(sqlpage.environment_variable(''SQLPAGE_SITE_PREFIX''), '''') ||''/console'',
              ''title'', ''Console''
            --   ''submenu'', (
            --       SELECT json_group_array(
            --           json_object(
            --               ''title'', title,
            --               ''link'', COALESCE(sqlpage.environment_variable(''SQLPAGE_SITE_PREFIX''), '''') || link,
            --               ''description'', description
            --           )
            --       )
            --       FROM (
            --           SELECT
            --               COALESCE(abbreviated_caption, caption) as title,
            --               COALESCE(url, path_href) as link,
            --               description
            --           FROM spry_route
            --           WHERE path_href = ''/spry/console''
            --           ORDER BY sibling_order
            --       )
            --   )
          ) as menu_item,       
       ''Spry BaaS v0.0.1 Web UI ([SQLPage v'' || sqlpage.version() || ''](https://sql-page.com/documentation.sql)) '' || ''📄 ['' || substr(sqlpage.path(), 2) || '']('' || COALESCE(sqlpage.environment_variable(''SQLPAGE_SITE_PREFIX''), '''') || ''/spry/console/sqlpage-files/sqlpage-file.sql?path='' || substr(sqlpage.path(), LENGTH(COALESCE(sqlpage.environment_variable(''SQLPAGE_SITE_PREFIX''), '''')) + 2 ) || '')'' as footer;

SELECT ''text'' AS component, sqlpage.path() as contents;

', CURRENT_TIMESTAMP);
-- tail SQL defined in `DeploySQL.tailSQL` (file:///home/<USER>/workspaces/spry/lib/engine/orchestrate.ts:536:46) (begin)
-- tail SQL defined in `DeploySQL.tailSQL` (file:///home/<USER>/workspaces/spry/lib/engine/orchestrate.ts:540:46) (end)
