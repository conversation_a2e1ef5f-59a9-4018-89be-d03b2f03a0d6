# -----------------------------------------------------------------------------
# Generated by: debug-foundry.env.sh
#
# This output was produced by a Foundry script that runs as part of the Spry
# orchestration pipeline. It demonstrates:
#
#   • Which FOUNDRY_* environment variables the Spry engine makes available.
#   • How FOUNDRY_SOURCE and FOUNDRY_CONTEXT_JSON can be flattened into
#     deterministic key=value pairs for inspection.
#   • That even a non-TypeScript Foundry (here, a Bash script) can generate
#     dynamic, auto-materialized output fully integrated into the Spry build.
#
# The contents below are not static: each run reflects the current Spry
# orchestration context. This file is auto-materialized by <PERSON><PERSON>ry following the
# <basename>.auto.<nature> naming convention, so you can check it into source
# control for reproducibility or inspect it to debug orchestration state.
# -----------------------------------------------------------------------------

# FOUNDRY_* (exported env)
FOUNDRY_AUTO_MATERIALIZE=TRUE
FOUNDRY_CONTEXT_JSON={"cliOpts":{"dbName":"sqlpage.db","cleanDb":false},"cwd":"/home/<USER>/workspaces/spry/support/assurance/e2e-prime","projectPaths":{"projectFsPaths":{"identity":"project","root":"/home/<USER>/workspaces/spry/support/assurance/e2e-prime/"},"projectSrcFsPaths":{"identity":"project-src","root":"/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src"},"webPaths":{},"spryDropIn":{"fsHome":"/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src/spry.d","fsAuto":"/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src/spry.d/auto","webHome":"spry.d","webAuto":"spry.d/auto"},"spryStd":{"homeFromSymlink":"../../../../lib/std","absPathToLocal":"/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src/spry","relPathToHome":"src/spry"},"sqlPage":{"absPathToConfDir":"/home/<USER>/workspaces/spry/support/assurance/e2e-prime/sqlpage"},"devWatchRoots":["src","../../../lib/std"]}}
FOUNDRY_MATERIALIZE_BASENAME=debug-foundry.auto.env
FOUNDRY_MATERIALIZE_PATH=/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src/debug-foundry.auto.env
FOUNDRY_PROJECT_HOME=/home/<USER>/workspaces/spry/support/assurance/e2e-prime/
FOUNDRY_PROJECT_ID=project
FOUNDRY_PROJECT_SPRYD_AUTO=/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src/spry.d/auto
FOUNDRY_PROJECT_SPRYD_HOME=/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src/spry.d
FOUNDRY_PROJECT_SRC_HOME=/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src
FOUNDRY_SOURCE_JSON={"we":{"origin":{"paths":{"identity":"project","root":"/home/<USER>/workspaces/spry/support/assurance/e2e-prime/"},"options":{"includeDirs":false,"includeFiles":true,"includeSymlinks":false,"followSymlinks":true,"canonicalize":true}},"entry":{"path":"/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src/debug-foundry.env.sh","name":"debug-foundry.env.sh","isFile":true,"isDirectory":false,"isSymlink":false}},"ann":{"nature":"foundry","runBeforeAnnCatalog":true,"runAfterAnnCatalog":false,"isCleanable":true,"dependsOn":"none","absFsPath":"/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src/debug-foundry.env.sh","relFsPath":"src/debug-foundry.env.sh","webPath":"debug-foundry.env.sh","isSystemGenerated":false},"pfn":{"materialize":{"auto":true,"basename":"debug-foundry.auto.env","path":"/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src/debug-foundry.auto.env"},"fileName":"debug-foundry.env.sh","base":"debug-foundry","nature":"env","extn":"sh"}}
FOUNDRY_TARGET_SQLITEDB=/home/<USER>/workspaces/spry/support/assurance/e2e-prime/sqlpage.db
FOUNDRY_WORKFLOW_STEP=BEFORE_ANN_CATALOG

# FOUNDRY_SOURCE_JSON -> FOUNDRY_SRC_*
FOUNDRY_SRC_ANN__ABSFSPATH=/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src/debug-foundry.env.sh
FOUNDRY_SRC_ANN__DEPENDSON=none
FOUNDRY_SRC_ANN__ISCLEANABLE=true
FOUNDRY_SRC_ANN__ISSYSTEMGENERATED=false
FOUNDRY_SRC_ANN__NATURE=foundry
FOUNDRY_SRC_ANN__RELFSPATH=src/debug-foundry.env.sh
FOUNDRY_SRC_ANN__RUNAFTERANNCATALOG=false
FOUNDRY_SRC_ANN__RUNBEFOREANNCATALOG=true
FOUNDRY_SRC_ANN__WEBPATH=debug-foundry.env.sh
FOUNDRY_SRC_PFN__BASE=debug-foundry
FOUNDRY_SRC_PFN__EXTN=sh
FOUNDRY_SRC_PFN__FILENAME=debug-foundry.env.sh
FOUNDRY_SRC_PFN__MATERIALIZE__AUTO=true
FOUNDRY_SRC_PFN__MATERIALIZE__BASENAME=debug-foundry.auto.env
FOUNDRY_SRC_PFN__MATERIALIZE__PATH=/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src/debug-foundry.auto.env
FOUNDRY_SRC_PFN__NATURE=env
FOUNDRY_SRC_WE__ENTRY__ISDIRECTORY=false
FOUNDRY_SRC_WE__ENTRY__ISFILE=true
FOUNDRY_SRC_WE__ENTRY__ISSYMLINK=false
FOUNDRY_SRC_WE__ENTRY__NAME=debug-foundry.env.sh
FOUNDRY_SRC_WE__ENTRY__PATH=/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src/debug-foundry.env.sh
FOUNDRY_SRC_WE__ORIGIN__OPTIONS__CANONICALIZE=true
FOUNDRY_SRC_WE__ORIGIN__OPTIONS__FOLLOWSYMLINKS=true
FOUNDRY_SRC_WE__ORIGIN__OPTIONS__INCLUDEDIRS=false
FOUNDRY_SRC_WE__ORIGIN__OPTIONS__INCLUDEFILES=true
FOUNDRY_SRC_WE__ORIGIN__OPTIONS__INCLUDESYMLINKS=false
FOUNDRY_SRC_WE__ORIGIN__PATHS__IDENTITY=project
FOUNDRY_SRC_WE__ORIGIN__PATHS__ROOT=/home/<USER>/workspaces/spry/support/assurance/e2e-prime/

# FOUNDRY_CONTEXT_JSON -> FOUNDRY_CTX_*
FOUNDRY_CTX_CLIOPTS__CLEANDB=false
FOUNDRY_CTX_CLIOPTS__DBNAME=sqlpage.db
FOUNDRY_CTX_CWD=/home/<USER>/workspaces/spry/support/assurance/e2e-prime
FOUNDRY_CTX_PROJECTPATHS__DEVWATCHROOTS__0=src
FOUNDRY_CTX_PROJECTPATHS__DEVWATCHROOTS__1=../../../lib/std
FOUNDRY_CTX_PROJECTPATHS__PROJECTFSPATHS__IDENTITY=project
FOUNDRY_CTX_PROJECTPATHS__PROJECTFSPATHS__ROOT=/home/<USER>/workspaces/spry/support/assurance/e2e-prime/
FOUNDRY_CTX_PROJECTPATHS__PROJECTSRCFSPATHS__IDENTITY=project-src
FOUNDRY_CTX_PROJECTPATHS__PROJECTSRCFSPATHS__ROOT=/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src
FOUNDRY_CTX_PROJECTPATHS__SPRYDROPIN__FSAUTO=/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src/spry.d/auto
FOUNDRY_CTX_PROJECTPATHS__SPRYDROPIN__FSHOME=/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src/spry.d
FOUNDRY_CTX_PROJECTPATHS__SPRYDROPIN__WEBAUTO=spry.d/auto
FOUNDRY_CTX_PROJECTPATHS__SPRYDROPIN__WEBHOME=spry.d
FOUNDRY_CTX_PROJECTPATHS__SPRYSTD__ABSPATHTOLOCAL=/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src/spry
FOUNDRY_CTX_PROJECTPATHS__SPRYSTD__HOMEFROMSYMLINK=../../../../lib/std
FOUNDRY_CTX_PROJECTPATHS__SPRYSTD__RELPATHTOHOME=src/spry
FOUNDRY_CTX_PROJECTPATHS__SQLPAGE__ABSPATHTOCONFDIR=/home/<USER>/workspaces/spry/support/assurance/e2e-prime/sqlpage
