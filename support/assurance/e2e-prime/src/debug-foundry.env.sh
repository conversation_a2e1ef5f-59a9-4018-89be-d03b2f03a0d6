#!/usr/bin/env bash
# @spry.nature foundry @spry.isCleanable
#
# 👆 Two things make <PERSON><PERSON><PERSON> notice this file:
#   1. The file has its "executable bit" set (`chmod +x`), so the OS can run it.
#   2. The `spry.nature foundry` annotation above tells <PERSON><PERSON><PERSON> this is a
#      executable (Foundry) that should be included in the build.
#
# S<PERSON>ry will automatically scan source files for these annotations. It uses
# the shared `/lib/universal/content` module to read and understand them.
# That module knows how to parse annotations from many types of files, not
# just Bash scripts. Check the README in that library if you need details on
# how annotation scanning works.
#
# 🔄 Auto-materialization:
#   - When <PERSON><PERSON><PERSON> runs this file, anything it prints to STDOUT is captured.
#   - If the file name follows the pattern <basename>.<nature>.<runner>
#     (for example: `debug-foundry.env.sh`), <PERSON><PERSON><PERSON> will automatically
#     write the output into a file named <basename>.auto.<nature>
#     (for example: `debug-foundry.auto.env`).
#   - This is the simplest way to produce build artifacts from a Foundry.
#   - If the file is auto-materialized and spry.isCleanable is set, it can be
#     deleted during a `clean` operation.
#   - If the file is not auto-materialized and spry.isCleanable is set, then
#     FOUNDRY_DESTROY_CLEAN env var will be set to 'TRUE' and the cap exec
#     can decide what to do.
#
# 🗂️ Custom file outputs:
#   - You are not limited to auto-materialization. Foundrys can also write
#     their own single or multiple files wherever they need to.
#   - Spry passes a set of `FOUNDRY_*` environment variables that tell you
#     the proper destinations (for example, the SQLite database file path
#     or the auto-materialize path).
#   - Your script can choose to ignore auto-materialization and instead
#     create files in those locations manually.
#
# In short: this file demonstrates the auto-materialization convention,
# but Foundrys are free to manage their own outputs too. Either way, Spry
# integrates the results into the build pipeline.
# -----------------------------------------------------------------------------
# This debug-foundry.env.sh Foundry script exists only as a teaching tool.
# -----------------------------------------------------------------------------
# Purpose:
#   - Show which FOUNDRY_* environment variables Spry injects into a Foundry.
#   - Demonstrate how to flatten complex JSON env vars like FOUNDRY_SOURCE
#     and FOUNDRY_CONTEXT_JSON into human-readable key=value lines.
#   - Confirm that Spry auto-materialization works: because this script follows
#     the <basename>.<nature>.<runner> pattern (e.g. debug-foundry.env.sh),
#     its stdout is automatically captured into a generated
#     <basename>.auto.<nature> file during the build.
#
# Key points:
#   - The script does not mutate anything in your project; it only prints.
#   - Output is dynamic (reflecting the current orchestration context).
#   - This is a non-TypeScript Foundry, proving that any language/runtime
#     capable of running as an executable can participate in the Spry pipeline.
#   - The resulting auto-materialized file becomes part of Spry’s reproducible
#     build artifacts alongside SQLPage content.
#
# In short:
#   This script is a simple "debug viewer" that lets you understand what
#   environment variables and context Spry provides to Foundrys, while showing
#   how non-TypeScript sources integrate seamlessly into the orchestration flow.
# -----------------------------------------------------------------------------

# --- helpers ---
has_jq() { command -v jq >/dev/null 2>&1; }
flatten_json() { # $1=JSON_STRING  $2=PREFIX (e.g., FOUNDRY_SRC or FOUNDRY_CTX)
  local j="$1" pfx="$2"
  if [[ -z "$j" ]]; then echo "${pfx}_ERROR=no JSON provided"; return; fi
  echo "$j" | jq -e . >/dev/null 2>&1 || { echo "${pfx}_ERROR=invalid JSON"; return; }
  echo "$j" | jq -r '
    def is_scalar(v): (v|type) as $t | ($t != "object" and $t != "array");
    def sanitize:
      tostring|ascii_upcase|gsub("[^A-Z0-9]";"_")
      | (if test("^[0-9]") then "_" + . else . end)
      | gsub("_+";"_") | gsub("^_+";"") | gsub("_+$";"");
    paths as $p
    | (getpath($p)) as $v
    | select(is_scalar($v))
    | ($p|map(sanitize)|join("__")) as $k
    | "'"$pfx"'_\($k)=\($v|tostring)"
  ' | sort
}

cat <<EOF
# -----------------------------------------------------------------------------
# Generated by: $(basename "$0")
#
# This output was produced by a Foundry script that runs as part of the Spry
# orchestration pipeline. It demonstrates:
#
#   • Which FOUNDRY_* environment variables the Spry engine makes available.
#   • How FOUNDRY_SOURCE and FOUNDRY_CONTEXT_JSON can be flattened into
#     deterministic key=value pairs for inspection.
#   • That even a non-TypeScript Foundry (here, a Bash script) can generate
#     dynamic, auto-materialized output fully integrated into the Spry build.
#
# The contents below are not static: each run reflects the current Spry
# orchestration context. This file is auto-materialized by Spry following the
# <basename>.auto.<nature> naming convention, so you can check it into source
# control for reproducibility or inspect it to debug orchestration state.
# -----------------------------------------------------------------------------

EOF

# --- exported env (sorted) ---
echo "# FOUNDRY_* (exported env)"
printenv | grep '^FOUNDRY_' | sort || true
echo

# --- FOUNDRY_SOURCE_JSON flattened (sorted) ---
echo "# FOUNDRY_SOURCE_JSON -> FOUNDRY_SRC_*"
if has_jq; then
  flatten_json "${FOUNDRY_SOURCE_JSON:-}" FOUNDRY_SRC
else
  echo "FOUNDRY_SRC_ERROR=jq not available"
fi
echo

# --- FOUNDRY_CONTEXT_JSON flattened (sorted) ---
echo "# FOUNDRY_CONTEXT_JSON -> FOUNDRY_CTX_*"
if has_jq; then
  flatten_json "${FOUNDRY_CONTEXT_JSON:-}" FOUNDRY_CTX
else
  echo "FOUNDRY_CTX_ERROR=jq not available"
fi
