{"database": {"sqlite_version": "3.39.2", "database_list": [{"seq": 0, "name": "main", "file": "/home/<USER>/workspaces/spry/support/assurance/e2e-prime/sqlpage.db"}], "encoding": "UTF-8", "page_size": 4096, "auto_vacuum": 0, "journal_mode": "delete", "synchronous": 2, "user_version": 0, "application_id": 0, "foreign_keys": 0, "schema_version": 285, "collations": [{"seq": 0, "name": "decimal"}, {"seq": 1, "name": "uint"}, {"seq": 2, "name": "RTRIM"}, {"seq": 3, "name": "NOCASE"}, {"seq": 4, "name": "BINARY"}], "compile_options": ["ATOMIC_INTRINSICS=1", "COMPILER=clang-18.1.8", "DEFAULT_AUTOVACUUM", "DEFAULT_CACHE_SIZE=-2000", "DEFAULT_FILE_FORMAT=4", "DEFAULT_JOURNAL_SIZE_LIMIT=-1", "DEFAULT_MMAP_SIZE=0", "DEFAULT_PAGE_SIZE=4096", "DEFAULT_PCACHE_INITSZ=20", "DEFAULT_RECURSIVE_TRIGGERS", "DEFAULT_SECTOR_SIZE=4096", "DEFAULT_SYNCHRONOUS=2", "DEFAULT_WAL_AUTOCHECKPOINT=1000", "DEFAULT_WAL_SYNCHRONOUS=2", "DEFAULT_WORKER_THREADS=0", "ENABLE_COLUMN_METADATA", "ENABLE_DBPAGE_VTAB", "ENABLE_DBSTAT_VTAB", "ENABLE_EXPLAIN_COMMENTS", "ENABLE_FTS3", "ENABLE_FTS3_PARENTHESIS", "ENABLE_FTS4", "ENABLE_FTS5", "ENABLE_GEOPOLY", "ENABLE_MATH_FUNCTIONS", "ENABLE_PREUPDATE_HOOK", "ENABLE_RTREE", "ENABLE_SESSION", "ENABLE_STMTVTAB", "MALLOC_SOFT_LIMIT=1024", "MAX_ATTACHED=10", "MAX_COLUMN=2000", "MAX_COMPOUND_SELECT=500", "MAX_DEFAULT_PAGE_SIZE=8192", "MAX_EXPR_DEPTH=1000", "MAX_FUNCTION_ARG=127", "MAX_LENGTH=1000000000", "MAX_LIKE_PATTERN_LENGTH=50000", "MAX_MMAP_SIZE=0x7fff0000", "MAX_PAGE_COUNT=1073741823", "MAX_PAGE_SIZE=65536", "MAX_SQL_LENGTH=1000000000", "MAX_TRIGGER_DEPTH=1000", "MAX_VARIABLE_NUMBER=250000", "MAX_VDBE_OP=250000000", "MAX_WORKER_THREADS=8", "MUTEX_PTHREADS", "SYSTEM_MALLOC", "TEMP_STORE=1", "THREADSAFE=1"]}, "objects": {"tables": [{"name": "sqlpage_files", "createSql": "CREATE TABLE \"sqlpage_files\" (\n  \"path\" VARCHAR PRIMARY KEY NOT NULL,\n  \"contents\" TEXT NOT NULL,\n  \"last_modified\" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP\n)", "rootpage": 2, "isVirtual": 0, "module": null, "moduleArgsRaw": null, "withoutRowid": 0, "strict": 0, "hasAutoincrement": 0, "rowidTable": 1, "hasChecks": 0, "checkCount": 0, "primaryKey": [{"pk": 1, "cid": 0, "name": "path"}], "columns": [{"cid": 0, "name": "path", "type": "VARCHAR", "notnull": 1, "dflt_value": null, "pk": 1, "hidden": 0, "generated": 0}, {"cid": 1, "name": "contents", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0, "hidden": 0, "generated": 0}, {"cid": 2, "name": "last_modified", "type": "TIMESTAMPTZ", "notnull": 0, "dflt_value": "CURRENT_TIMESTAMP", "pk": 0, "hidden": 0, "generated": 0}], "foreignKeys": [], "indexes": [{"name": "idx_entry_relfs", "rootpage": 18, "createSql": "CREATE INDEX idx_entry_relfs\n  ON sqlpage_files (json_extract(contents, '$.webPath'))\n  WHERE path GLOB 'spry.d/auto/entry/**/*.auto.json'", "unique": 0, "origin": "c", "partial": 1, "where": "path GLOB 'spry.d/auto/entry/**/*.auto.json'", "columns": [{"seqno": 0, "cid": -2, "name": null}], "xinfo": [{"seqno": 0, "cid": -2, "name": null, "desc": 0, "coll": "BINARY", "key": 1, "isExpr": 1}, {"seqno": 1, "cid": -1, "name": null, "desc": 0, "coll": "BINARY", "key": 0, "isExpr": 1}]}, {"name": "idx_route_edges_json", "rootpage": 17, "createSql": "CREATE INDEX idx_route_edges_json\n  ON sqlpage_files (json_extract(contents))\n  WHERE path GLOB 'spry.d/auto/route/*edges*.auto.json'", "unique": 0, "origin": "c", "partial": 1, "where": "path GLOB 'spry.d/auto/route/*edges*.auto.json'", "columns": [{"seqno": 0, "cid": -2, "name": null}], "xinfo": [{"seqno": 0, "cid": -2, "name": null, "desc": 0, "coll": "BINARY", "key": 1, "isExpr": 1}, {"seqno": 1, "cid": -1, "name": null, "desc": 0, "coll": "BINARY", "key": 0, "isExpr": 1}]}, {"name": "idx_route_edges_dir", "rootpage": 16, "createSql": "CREATE INDEX idx_route_edges_dir\n  ON sqlpage_files (path)\n  WHERE path GLOB 'spry.d/auto/route/*edges*.auto.json'", "unique": 0, "origin": "c", "partial": 1, "where": "path GLOB 'spry.d/auto/route/*edges*.auto.json'", "columns": [{"seqno": 0, "cid": 0, "name": "path"}], "xinfo": [{"seqno": 0, "cid": 0, "name": "path", "desc": 0, "coll": "BINARY", "key": 1, "isExpr": 0}, {"seqno": 1, "cid": -1, "name": null, "desc": 0, "coll": "BINARY", "key": 0, "isExpr": 1}]}, {"name": "idx_breadcrumbs_json", "rootpage": 14, "createSql": "CREATE INDEX idx_breadcrumbs_json\n  ON sqlpage_files (json_extract(contents))\n  WHERE path GLOB 'spry.d/auto/breadcrumbs/**/*.auto.json'", "unique": 0, "origin": "c", "partial": 1, "where": "path GLOB 'spry.d/auto/breadcrumbs/**/*.auto.json'", "columns": [{"seqno": 0, "cid": -2, "name": null}], "xinfo": [{"seqno": 0, "cid": -2, "name": null, "desc": 0, "coll": "BINARY", "key": 1, "isExpr": 1}, {"seqno": 1, "cid": -1, "name": null, "desc": 0, "coll": "BINARY", "key": 0, "isExpr": 1}]}, {"name": "idx_breadcrumbs_dir", "rootpage": 13, "createSql": "CREATE INDEX idx_breadcrumbs_dir\n  ON sqlpage_files (path)\n  WHERE path GLOB 'spry.d/auto/breadcrumbs/**/*.auto.json'", "unique": 0, "origin": "c", "partial": 1, "where": "path GLOB 'spry.d/auto/breadcrumbs/**/*.auto.json'", "columns": [{"seqno": 0, "cid": 0, "name": "path"}], "xinfo": [{"seqno": 0, "cid": 0, "name": "path", "desc": 0, "coll": "BINARY", "key": 1, "isExpr": 0}, {"seqno": 1, "cid": -1, "name": null, "desc": 0, "coll": "BINARY", "key": 0, "isExpr": 1}]}, {"name": "idx_route_json_scan", "rootpage": 12, "createSql": "CREATE INDEX idx_route_json_scan\n  ON sqlpage_files (json(contents))  -- or (contents -> '$') if you prefer\n  WHERE path GLOB 'spry.d/auto/route/**/*.auto.json'", "unique": 0, "origin": "c", "partial": 1, "where": "path GLOB 'spry.d/auto/route/**/*.auto.json'", "columns": [{"seqno": 0, "cid": -2, "name": null}], "xinfo": [{"seqno": 0, "cid": -2, "name": null, "desc": 0, "coll": "BINARY", "key": 1, "isExpr": 1}, {"seqno": 1, "cid": -1, "name": null, "desc": 0, "coll": "BINARY", "key": 0, "isExpr": 1}]}, {"name": "idx_route_json_path_flat", "rootpage": 11, "createSql": "CREATE INDEX idx_route_json_path_flat\n  ON sqlpage_files (contents ->> '$.path')\n  WHERE path GLOB 'spry.d/auto/route/**/*.auto.json'", "unique": 0, "origin": "c", "partial": 1, "where": "path GLOB 'spry.d/auto/route/**/*.auto.json'", "columns": [{"seqno": 0, "cid": -2, "name": null}], "xinfo": [{"seqno": 0, "cid": -2, "name": null, "desc": 0, "coll": "BINARY", "key": 1, "isExpr": 1}, {"seqno": 1, "cid": -1, "name": null, "desc": 0, "coll": "BINARY", "key": 0, "isExpr": 1}]}, {"name": "idx_entry_webpath", "rootpage": 8, "createSql": "CREATE INDEX idx_entry_webpath\n  ON sqlpage_files (contents ->> '$.webPath')\n  WHERE path GLOB 'spry.d/auto/entry/**/*.auto.json'", "unique": 0, "origin": "c", "partial": 1, "where": "path GLOB 'spry.d/auto/entry/**/*.auto.json'", "columns": [{"seqno": 0, "cid": -2, "name": null}], "xinfo": [{"seqno": 0, "cid": -2, "name": null, "desc": 0, "coll": "BINARY", "key": 1, "isExpr": 1}, {"seqno": 1, "cid": -1, "name": null, "desc": 0, "coll": "BINARY", "key": 0, "isExpr": 1}]}, {"name": "idx_route_path", "rootpage": 7, "createSql": "CREATE INDEX idx_route_path\n  ON sqlpage_files (contents ->> '$.path')\n  WHERE path GLOB 'spry.d/auto/route/**/*.auto.json'", "unique": 0, "origin": "c", "partial": 1, "where": "path GLOB 'spry.d/auto/route/**/*.auto.json'", "columns": [{"seqno": 0, "cid": -2, "name": null}], "xinfo": [{"seqno": 0, "cid": -2, "name": null, "desc": 0, "coll": "BINARY", "key": 1, "isExpr": 1}, {"seqno": 1, "cid": -1, "name": null, "desc": 0, "coll": "BINARY", "key": 0, "isExpr": 1}]}, {"name": "idx_entry_source_json", "rootpage": 6, "createSql": "CREATE INDEX idx_entry_source_json\n  ON sqlpage_files (json_extract(contents, '$.\".source\"'))\n  WHERE path GLOB 'spry.d/auto/entry/**/*.auto.json' AND json_valid(contents)", "unique": 0, "origin": "c", "partial": 1, "where": "path GLOB 'spry.d/auto/entry/**/*.auto.json' AND json_valid(contents)", "columns": [{"seqno": 0, "cid": -2, "name": null}], "xinfo": [{"seqno": 0, "cid": -2, "name": null, "desc": 0, "coll": "BINARY", "key": 1, "isExpr": 1}, {"seqno": 1, "cid": -1, "name": null, "desc": 0, "coll": "BINARY", "key": 0, "isExpr": 1}]}, {"name": "idx_route_source_json", "rootpage": 5, "createSql": "CREATE INDEX idx_route_source_json\n  ON sqlpage_files (json_extract(contents, '$.\".source\"'))\n  WHERE path GLOB 'spry.d/auto/route/**/*.auto.json' AND json_valid(contents)", "unique": 0, "origin": "c", "partial": 1, "where": "path GLOB 'spry.d/auto/route/**/*.auto.json' AND json_valid(contents)", "columns": [{"seqno": 0, "cid": -2, "name": null}], "xinfo": [{"seqno": 0, "cid": -2, "name": null, "desc": 0, "coll": "BINARY", "key": 1, "isExpr": 1}, {"seqno": 1, "cid": -1, "name": null, "desc": 0, "coll": "BINARY", "key": 0, "isExpr": 1}]}, {"name": "idx_sqlpage_files_last_modified", "rootpage": 4, "createSql": "CREATE INDEX idx_sqlpage_files_last_modified\n  ON sqlpage_files (last_modified)", "unique": 0, "origin": "c", "partial": 0, "where": null, "columns": [{"seqno": 0, "cid": 2, "name": "last_modified"}], "xinfo": [{"seqno": 0, "cid": 2, "name": "last_modified", "desc": 0, "coll": "BINARY", "key": 1, "isExpr": 0}, {"seqno": 1, "cid": -1, "name": null, "desc": 0, "coll": "BINARY", "key": 0, "isExpr": 1}]}, {"name": "sqlite_autoindex_sqlpage_files_1", "rootpage": 3, "createSql": null, "unique": 1, "origin": "pk", "partial": 0, "where": null, "columns": [{"seqno": 0, "cid": 0, "name": "path"}], "xinfo": [{"seqno": 0, "cid": 0, "name": "path", "desc": 0, "coll": "BINARY", "key": 1, "isExpr": 0}, {"seqno": 1, "cid": -1, "name": null, "desc": 0, "coll": "BINARY", "key": 0, "isExpr": 1}]}], "uniqueConstraints": [], "triggers": []}], "views": [{"name": "spry_annotation", "createSql": "CREATE VIEW spry_annotation AS\nWITH route_files AS (\n  SELECT\n    contents,\n    contents ->> '$.path' AS path\n  FROM sqlpage_files\n  WHERE path GLOB 'spry.d/auto/route/**/*.auto.json'\n    AND json_valid(contents)\n    AND json_type(contents, '$.path') = 'text'\n    AND json_type(contents, '$.\".source\"') = 'object'\n),\nentry_files AS (\n  SELECT\n    contents,\n    contents ->> '$.webPath' AS path\n  FROM sqlpage_files\n  WHERE path GLOB 'spry.d/auto/entry/**/*.auto.json'\n    AND json_valid(contents)\n    AND json_type(contents, '$.webPath') = 'text'\n    AND json_type(contents, '$.\".source\"') = 'object'\n),\nroute_ann AS (\n  SELECT\n    rf.path                         AS path_spf,\n    CASE\n      WHEN substr(rf.path,1,1)='/' THEN rf.path\n      ELSE '/' || rf.path\n    END                             AS path_href,\n    'route'                         AS namespace,\n    a.key                           AS annotation,\n    a.value ->> '$.id'              AS id,\n    a.value ->> '$.key'             AS key,\n    a.value ->> '$.kind'            AS kind,\n    a.value ->> '$.value'           AS value,\n    a.value ->> '$.raw'             AS raw,\n    a.value ->  '$.source'          AS source\n  FROM route_files rf,\n       json_each(rf.contents, '$.\".source\"') AS a\n),\nentry_ann AS (\n  SELECT\n    ef.path                         AS path,\n    CASE\n      WHEN substr(ef.path,1,1)='/' THEN ef.path\n      ELSE '/' || ef.path\n    END                             AS path_href,\n    'entry'                         AS namespace,\n    a.key                           AS annotation,\n    a.value ->> '$.id'              AS id,\n    a.value ->> '$.key'             AS key,\n    a.value ->> '$.kind'            AS kind,\n    a.value ->> '$.value'           AS value,\n    a.value ->> '$.raw'             AS raw,\n    a.value ->  '$.source'          AS source\n  FROM entry_files ef,\n       json_each(ef.contents, '$.\".source\"') AS a\n)\nSELECT * FROM route_ann\nUNION ALL\nSELECT * FROM entry_ann", "rootpage": 0, "columns": [{"cid": 0, "name": "path_spf", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 1, "name": "path_href", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 2, "name": "namespace", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 3, "name": "annotation", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 4, "name": "id", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 5, "name": "key", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 6, "name": "kind", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 7, "name": "value", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 8, "name": "raw", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 9, "name": "source", "type": "", "notnull": 0, "dflt_value": null}]}, {"name": "spry_route", "createSql": "CREATE VIEW spry_route AS\nWITH f AS (\n  SELECT\n    path          AS spf_path,\n    last_modified AS spf_last_modified,\n    contents,\n    substr(\n      path,\n      length('spry.d/auto/route/') + 1,\n      length(path) - length('spry.d/auto/route/') - length('.auto.json')\n    )             AS path_spf_target\n  FROM sqlpage_files\n  WHERE path GLOB 'spry.d/auto/route/**/*.auto.json'\n    AND json_valid(contents)\n)\nSELECT\n  contents ->> '$.path'                AS \"path_spf\",\n  path_spf_target                      AS \"path_spf_target\",\n  CASE\n    WHEN substr(path_spf_target,1,1)='/' THEN path_spf_target\n    ELSE '/' || path_spf_target\n  END                                  AS path_href,  \n  contents ->> '$.pathBasename'        AS \"path_basename\",\n  contents ->> '$.pathBasenameNoExtn'  AS \"path_basename_no_extn\",\n  contents ->> '$.pathDirname'         AS \"path_dirname\",\n  contents ->> '$.pathExtnTerminal'    AS \"path_extn_terminal\",\n  contents ->  '$.pathExtns'           AS \"path_extns\",\n\n  contents ->> '$.caption'             AS \"caption\",\n  contents ->> '$.siblingOrder'        AS \"sibling_order\",\n  contents ->> '$.url'                 AS \"url\",\n  contents ->> '$.title'               AS \"title\",\n  contents ->> '$.abbreviatedCaption'  AS \"abbreviated_caption\",\n  contents ->> '$.description'         AS \"description\",\n  contents ->  '$.elaboration'         AS \"elaboration\",\n\n  spf_path,\n  spf_last_modified\nFROM f\nWHERE json_type(contents, '$.path') = 'text'", "rootpage": 0, "columns": [{"cid": 0, "name": "path_spf", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 1, "name": "path_spf_target", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 2, "name": "path_href", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 3, "name": "path_basename", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 4, "name": "path_basename_no_extn", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 5, "name": "path_dirname", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 6, "name": "path_extn_terminal", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 7, "name": "path_extns", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 8, "name": "caption", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 9, "name": "sibling_order", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 10, "name": "url", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 11, "name": "title", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 12, "name": "abbreviated_caption", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 13, "name": "description", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 14, "name": "elaboration", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 15, "name": "spf_path", "type": "VARCHAR", "notnull": 0, "dflt_value": null}, {"cid": 16, "name": "spf_last_modified", "type": "TIMESTAMPTZ", "notnull": 0, "dflt_value": null}]}, {"name": "spry_route_crumb", "createSql": "CREATE VIEW spry_route_crumb AS\nWITH files AS (\n  SELECT\n    f.path          AS src_path,\n    f.last_modified AS src_last_modified,\n    f.contents,\n    -- filename-derived logical path (strip prefix/suffix)\n    substr(\n      f.path,\n      length('spry.d/auto/breadcrumbs/') + 1,\n      length(f.path) - length('spry.d/auto/breadcrumbs/') - length('.auto.json')\n    ) AS path_spf_target\n  FROM sqlpage_files AS f\n  WHERE f.path GLOB 'spry.d/auto/breadcrumbs/**/*.auto.json'\n    AND json_valid(f.contents)\n),\n-- One row per crumb (array element)\nraw AS (\n  SELECT\n    files.src_path,\n    files.src_last_modified,\n    files.path_spf_target,                     -- qualified to avoid json_each.path name\n    CAST(c.key AS INTEGER) AS crumb_index,\n    c.value                AS crumb\n  FROM files\n  JOIN json_each(files.contents) AS c\n)\nSELECT\n  -- logical path derived from the filename\n  path_spf_target AS path_spf,\n  CASE\n    WHEN substr(path_spf_target,1,1)='/' THEN path_spf_target\n    ELSE '/' || path_spf_target\n  END                               AS path_href,\n  crumb_index,\n\n  -- hrefs.* (top-level)\n  crumb ->> '$.hrefs.canonical'     AS href_canonical,\n  crumb ->> '$.hrefs.index'         AS href_index,\n  crumb ->> '$.hrefs.trailingSlash' AS href_trailing_slash,\n\n  -- node.* (top-level)\n  crumb ->> '$.node.virtual'  AS node_virtual,\n  crumb ->> '$.node.basename' AS node_basename,\n  crumb ->> '$.node.path'     AS node_path,\n\n  -- provenance\n  src_path            AS breadcrumbs_source_path,\n  src_last_modified   AS breadcrumbs_source_last_modified\nFROM raw\nWHERE json_type(crumb, '$.node.path') = 'text'\nORDER BY src_path, crumb_index", "rootpage": 0, "columns": [{"cid": 0, "name": "path_spf", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 1, "name": "path_href", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 2, "name": "crumb_index", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 3, "name": "href_canonical", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 4, "name": "href_index", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 5, "name": "href_trailing_slash", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 6, "name": "node_virtual", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 7, "name": "node_basename", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 8, "name": "node_path", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 9, "name": "breadcrumbs_source_path", "type": "VARCHAR", "notnull": 0, "dflt_value": null}, {"cid": 10, "name": "breadcrumbs_source_last_modified", "type": "TIMESTAMPTZ", "notnull": 0, "dflt_value": null}]}, {"name": "spry_route_edge", "createSql": "CREATE VIEW spry_route_edge AS\nWITH files AS (\n  SELECT\n    f.path          AS src_path,\n    f.last_modified AS src_last_modified,\n    f.contents\n  FROM sqlpage_files AS f\n  WHERE f.path GLOB 'spry.d/auto/route/*edges*.auto.json'\n    AND json_valid(f.contents)\n),\nedges_raw AS (\n  SELECT\n    src_path,\n    src_last_modified,\n    json_extract(e.value, '$.parent') AS parent_path_spf,\n    json_extract(e.value, '$.child')  AS child_path_spf\n  FROM files, json_each(files.contents) AS e\n  WHERE json_type(e.value, '$.parent') = 'text'\n    AND json_type(e.value, '$.child')  = 'text'\n),\nranked AS (\n  SELECT\n    parent_path_spf,\n    child_path_spf,\n    src_path,\n    src_last_modified,\n    ROW_NUMBER() OVER (\n      PARTITION BY parent_path_spf, child_path_spf\n      ORDER BY src_last_modified DESC, src_path DESC\n    ) AS rn\n  FROM edges_raw\n)\nSELECT\n  parent_path_spf,\n  child_path_spf,\n  CASE\n    WHEN substr(parent_path_spf,1,1)='/' THEN parent_path_spf\n    ELSE '/' || parent_path_spf\n  END               AS parent_path_href,\n  CASE\n    WHEN substr(child_path_spf,1,1)='/' THEN child_path_spf\n    ELSE '/' || child_path_spf\n  END               AS child_path_href,\n  src_path          AS edges_source_path,\n  src_last_modified AS edges_source_last_modified\nFROM ranked\nWHERE rn = 1", "rootpage": 0, "columns": [{"cid": 0, "name": "parent_path_spf", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 1, "name": "child_path_spf", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 2, "name": "parent_path_href", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 3, "name": "child_path_href", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 4, "name": "edges_source_path", "type": "VARCHAR", "notnull": 0, "dflt_value": null}, {"cid": 5, "name": "edges_source_last_modified", "type": "TIMESTAMPTZ", "notnull": 0, "dflt_value": null}]}, {"name": "spry_route_child", "createSql": "CREATE VIEW spry_route_child AS\nSELECT\n  e.parent_path_spf,\n  e.parent_path_href,\n  c.*,\n  e.edges_source_path,\n  e.edges_source_last_modified\nFROM spry_route_edge AS e\nJOIN spry_route     AS c\n  ON c.\"path_spf\" = e.child_path_spf\nORDER BY e.parent_path_spf, c.\"path_spf\"", "rootpage": 0, "columns": [{"cid": 0, "name": "parent_path_spf", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 1, "name": "parent_path_href", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 2, "name": "path_spf", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 3, "name": "path_spf_target", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 4, "name": "path_href", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 5, "name": "path_basename", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 6, "name": "path_basename_no_extn", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 7, "name": "path_dirname", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 8, "name": "path_extn_terminal", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 9, "name": "path_extns", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 10, "name": "caption", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 11, "name": "sibling_order", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 12, "name": "url", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 13, "name": "title", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 14, "name": "abbreviated_caption", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 15, "name": "description", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 16, "name": "elaboration", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 17, "name": "spf_path", "type": "VARCHAR", "notnull": 0, "dflt_value": null}, {"cid": 18, "name": "spf_last_modified", "type": "TIMESTAMPTZ", "notnull": 0, "dflt_value": null}, {"cid": 19, "name": "edges_source_path", "type": "VARCHAR", "notnull": 0, "dflt_value": null}, {"cid": 20, "name": "edges_source_last_modified", "type": "TIMESTAMPTZ", "notnull": 0, "dflt_value": null}]}, {"name": "spry_entry", "createSql": "CREATE VIEW spry_entry AS\nWITH files AS (\n  SELECT\n    f.path          AS src_path,\n    f.last_modified AS src_last_modified,\n    f.contents,\n    substr(\n      f.path,\n      length('spry.d/auto/entry/') + 1,\n      length(f.path) - length('spry.d/auto/entry/') - length('.auto.json')\n    ) AS path_spf_target\n  FROM sqlpage_files AS f\n  WHERE f.path GLOB 'spry.d/auto/entry/**/*.auto.json'\n    AND json_valid(f.contents)\n)\nSELECT\n  files.contents ->> '$.webPath'   AS path_spf,\n  CASE\n    WHEN substr(path_spf_target,1,1)='/' THEN path_spf_target\n    ELSE '/' || path_spf_target\n  END                              AS path_href,\n  files.contents ->> '$.nature'    AS nature,\n  files.contents ->> '$.relFsPath' AS rel_fs_path,\n  files.src_path            AS entry_source_path,\n  files.src_last_modified   AS entry_source_last_modified\nFROM files\nWHERE json_type(files.contents, '$.webPath') = 'text'", "rootpage": 0, "columns": [{"cid": 0, "name": "path_spf", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 1, "name": "path_href", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 2, "name": "nature", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 3, "name": "rel_fs_path", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 4, "name": "entry_source_path", "type": "VARCHAR", "notnull": 0, "dflt_value": null}, {"cid": 5, "name": "entry_source_last_modified", "type": "TIMESTAMPTZ", "notnull": 0, "dflt_value": null}]}, {"name": "spry_schema_info_table", "createSql": "CREATE VIEW spry_schema_info_table AS\nSELECT\n  'main'                                    AS schema_name,\n  t.key                                     AS table_name,\n  json_extract(t.value,'$.type')            AS type,\n  json_extract(t.value,'$.ncol')            AS ncol,\n  json_extract(t.value,'$.strict')          AS strict,\n  json_extract(t.value,'$.without_rowid')   AS without_rowid,\n  json_extract(t.value,'$.sql')             AS definition_sql\nFROM sqlpage_files AS s,\n     json_each(s.contents, '$.tables') AS t\nWHERE s.path = 'spry.d/info-schema.auto.json'", "rootpage": 0, "columns": [{"cid": 0, "name": "schema_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 1, "name": "table_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 2, "name": "type", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 3, "name": "ncol", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 4, "name": "strict", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 5, "name": "without_rowid", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 6, "name": "definition_sql", "type": "", "notnull": 0, "dflt_value": null}]}, {"name": "spry_schema_info_table_column", "createSql": "CREATE VIEW spry_schema_info_table_column AS\nSELECT\n  'main'                                        AS schema_name,\n  t.key                                         AS table_name,\n  json_extract(c.value,'$.cid')                 AS cid,\n  json_extract(c.value,'$.name')                AS column_name,\n  json_extract(c.value,'$.type')                AS column_type,\n  json_extract(c.value,'$.notnull')             AS not_null,\n  json_extract(c.value,'$.dflt_value')          AS dflt_value,\n  json_extract(c.value,'$.pk')                  AS part_of_pk,\n  json_extract(c.value,'$.hidden')              AS hidden\nFROM sqlpage_files AS s,\n     json_each(s.contents, '$.tables') AS t,\n     json_each(t.value, '$.columns')           AS c\nWHERE s.path = 'spry.d/info-schema.auto.json'", "rootpage": 0, "columns": [{"cid": 0, "name": "schema_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 1, "name": "table_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 2, "name": "cid", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 3, "name": "column_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 4, "name": "column_type", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 5, "name": "not_null", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 6, "name": "dflt_value", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 7, "name": "part_of_pk", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 8, "name": "hidden", "type": "", "notnull": 0, "dflt_value": null}]}, {"name": "spry_schema_info_view", "createSql": "CREATE VIEW spry_schema_info_view AS\nSELECT\n  'main'                           AS schema_name,\n  v.key                            AS view_name,\n  json_extract(v.value,'$.type')   AS type,\n  json_extract(v.value,'$.sql')    AS definition_sql\nFROM sqlpage_files AS s,\n     json_each(s.contents, '$.views') AS v\nWHERE s.path = 'spry.d/info-schema.auto.json'", "rootpage": 0, "columns": [{"cid": 0, "name": "schema_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 1, "name": "view_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 2, "name": "type", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 3, "name": "definition_sql", "type": "", "notnull": 0, "dflt_value": null}]}, {"name": "spry_schema_info_view_column", "createSql": "CREATE VIEW spry_schema_info_view_column AS\nSELECT\n  'main'                          AS schema_name,\n  v.key                           AS view_name,\n  json_extract(vc.value,'$.cid')  AS cid,\n  json_extract(vc.value,'$.name') AS column_name,\n  json_extract(vc.value,'$.type') AS column_type,\n  json_extract(vc.value,'$.notnull') AS not_null,\n  json_extract(vc.value,'$.dflt_value') AS dflt_value\nFROM sqlpage_files AS s,\n     json_each(s.contents, '$.views') AS v\nLEFT JOIN json_each(v.value, '$.columns') AS vc ON 1=1\nWHERE s.path = 'spry.d/info-schema.auto.json'", "rootpage": 0, "columns": [{"cid": 0, "name": "schema_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 1, "name": "view_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 2, "name": "cid", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 3, "name": "column_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 4, "name": "column_type", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 5, "name": "not_null", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 6, "name": "dflt_value", "type": "", "notnull": 0, "dflt_value": null}]}, {"name": "spry_schema_info_index", "createSql": "CREATE VIEW spry_schema_info_index AS\nSELECT\n  'main'                                       AS schema_name,\n  t.key                                        AS table_name,\n  json_extract(i.value,'$.name')               AS index_name,\n  json_extract(i.value,'$.origin')             AS origin,      -- 'c','u','pk'\n  json_extract(i.value,'$.unique')             AS is_unique,\n  json_extract(i.value,'$.partial')            AS is_partial,\n  json_extract(i.value,'$.where')              AS definition_sql\nFROM sqlpage_files AS s,\n     json_each(s.contents, '$.tables')         AS t,\n     json_each(t.value, '$.indexes')           AS i\nWHERE s.path = 'spry.d/info-schema.auto.json'", "rootpage": 0, "columns": [{"cid": 0, "name": "schema_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 1, "name": "table_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 2, "name": "index_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 3, "name": "origin", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 4, "name": "is_unique", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 5, "name": "is_partial", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 6, "name": "definition_sql", "type": "", "notnull": 0, "dflt_value": null}]}, {"name": "spry_schema_info_index_column", "createSql": "CREATE VIEW spry_schema_info_index_column AS\nSELECT\n  'main'                                        AS schema_name,\n  t.key                                         AS table_name,\n  json_extract(i.value,'$.name')                AS index_name,\n  json_extract(ic.value,'$.seqno')              AS seqno,\n  json_extract(ic.value,'$.cid')                AS cid,\n  json_extract(ic.value,'$.name')               AS column_name,\n  json_extract(ic.value,'$.desc')               AS is_desc,\n  json_extract(ic.value,'$.coll')               AS collation_name,\n  json_extract(ic.value,'$.key')                AS is_key_column\nFROM sqlpage_files AS s,\n     json_each(s.contents, '$.tables')          AS t,\n     json_each(t.value, '$.indexes')            AS i,\n     json_each(i.value, '$.columns')            AS ic\nWHERE s.path = 'spry.d/info-schema.auto.json'", "rootpage": 0, "columns": [{"cid": 0, "name": "schema_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 1, "name": "table_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 2, "name": "index_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 3, "name": "seqno", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 4, "name": "cid", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 5, "name": "column_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 6, "name": "is_desc", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 7, "name": "collation_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 8, "name": "is_key_column", "type": "", "notnull": 0, "dflt_value": null}]}, {"name": "spry_schema_info_foreign_key", "createSql": "CREATE VIEW spry_schema_info_foreign_key AS\nSELECT\n  'main'                                   AS schema_name,\n  t.key                                    AS table_name,\n  json_extract(fk.value,'$.id')            AS fk_id,\n  json_extract(fk.value,'$.seq')           AS seq,\n  json_extract(fk.value,'$.from')          AS from_column,\n  json_extract(fk.value,'$.to')            AS to_column,\n  json_extract(fk.value,'$.table')         AS ref_table,\n  json_extract(fk.value,'$.on_update')     AS on_update,\n  json_extract(fk.value,'$.on_delete')     AS on_delete,\n  json_extract(fk.value,'$.match')         AS match\nFROM sqlpage_files AS s,\n     json_each(s.contents, '$.tables')     AS t,\n     json_each(t.value, '$.foreign_keys')  AS fk\nWHERE s.path = 'spry.d/info-schema.auto.json'", "rootpage": 0, "columns": [{"cid": 0, "name": "schema_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 1, "name": "table_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 2, "name": "fk_id", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 3, "name": "seq", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 4, "name": "from_column", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 5, "name": "to_column", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 6, "name": "ref_table", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 7, "name": "on_update", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 8, "name": "on_delete", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 9, "name": "match", "type": "", "notnull": 0, "dflt_value": null}]}, {"name": "spry_schema_info_table_trigger", "createSql": "CREATE VIEW spry_schema_info_table_trigger AS\nSELECT\n  'main'                                AS schema_name,\n  t.key                                 AS table_name,\n  json_extract(tr.value,'$.name')       AS trigger_name,\n  json_extract(tr.value,'$.sql')        AS definition_sql\nFROM sqlpage_files AS s,\n     json_each(s.contents, '$.tables')  AS t,\n     json_each(t.value, '$.triggers')   AS tr\nWHERE s.path = 'spry.d/info-schema.auto.json'", "rootpage": 0, "columns": [{"cid": 0, "name": "schema_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 1, "name": "table_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 2, "name": "trigger_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 3, "name": "definition_sql", "type": "", "notnull": 0, "dflt_value": null}]}, {"name": "spry_schema_info_trigger", "createSql": "CREATE VIEW spry_schema_info_trigger AS\nSELECT\n  'main'                                AS schema_name,\n  trg.key                               AS trigger_name,\n  json_extract(trg.value,'$.table')     AS table_name,\n  json_extract(trg.value,'$.sql')       AS definition_sql\nFROM sqlpage_files AS s,\n     json_each(s.contents, '$.triggers') AS trg\nWHERE s.path = 'spry.d/info-schema.auto.json'", "rootpage": 0, "columns": [{"cid": 0, "name": "schema_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 1, "name": "trigger_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 2, "name": "table_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 3, "name": "definition_sql", "type": "", "notnull": 0, "dflt_value": null}]}, {"name": "spry_schema_info_relation", "createSql": "CREATE VIEW spry_schema_info_relation AS\nSELECT\n  'main'                                  AS schema_name,\n  json_extract(r.value,'$.name')          AS relation_name,\n  json_extract(r.value,'$.from_table')    AS from_table,\n  json_extract(r.value,'$.to_table')      AS to_table,\n  json_extract(r.value,'$.type')          AS relation_type,\n  json_extract(r.value,'$.on_update')     AS on_update,\n  json_extract(r.value,'$.on_delete')     AS on_delete,\n  json_extract(r.value,'$.match')         AS match,\n  json_extract(r.value,'$.from_columns')  AS from_columns_json,\n  json_extract(r.value,'$.to_columns')    AS to_columns_json\nFROM sqlpage_files AS s,\n     json_each(s.contents, '$.relations') AS r\nWHERE s.path = 'spry.d/info-schema.auto.json'", "rootpage": 0, "columns": [{"cid": 0, "name": "schema_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 1, "name": "relation_name", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 2, "name": "from_table", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 3, "name": "to_table", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 4, "name": "relation_type", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 5, "name": "on_update", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 6, "name": "on_delete", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 7, "name": "match", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 8, "name": "from_columns_json", "type": "", "notnull": 0, "dflt_value": null}, {"cid": 9, "name": "to_columns_json", "type": "", "notnull": 0, "dflt_value": null}]}], "triggers": []}, ".context": {"sqliteDB": {"target": "/home/<USER>/workspaces/spry/support/assurance/e2e-prime/sqlpage.db", "kind": "file", "source": "env", "envKey": "FOUNDRY_TARGET_SQLITEDB"}, "foundryEnv": {"FOUNDRY_PROJECT_ID": "project", "FOUNDRY_PROJECT_HOME": "/home/<USER>/workspaces/spry/support/assurance/e2e-prime/", "FOUNDRY_SOURCE_JSON": "{\"we\":{\"origin\":{\"paths\":{\"identity\":\"project\",\"root\":\"/home/<USER>/workspaces/spry/support/assurance/e2e-prime/\"},\"options\":{\"includeDirs\":false,\"includeFiles\":true,\"includeSymlinks\":false,\"followSymlinks\":true,\"canonicalize\":true}},\"entry\":{\"path\":\"/home/<USER>/workspaces/spry/lib/std/lib/info-schema.json.ts\",\"name\":\"info-schema.json.ts\",\"isFile\":true,\"isDirectory\":false,\"isSymlink\":false}},\"ann\":{\"nature\":\"foundry\",\"runBeforeAnnCatalog\":true,\"runAfterAnnCatalog\":false,\"isCleanable\":true,\"dependsOn\":\"db-after-build\",\"absFsPath\":\"/home/<USER>/workspaces/spry/lib/std/lib/info-schema.json.ts\",\"relFsPath\":\"src/spry/lib/info-schema.json.ts\",\"webPath\":\"spry/lib/info-schema.json.ts\",\"isSystemGenerated\":false},\"pfn\":{\"materialize\":{\"auto\":true,\"basename\":\"info-schema.auto.json\",\"path\":\"/home/<USER>/workspaces/spry/lib/std/lib/info-schema.auto.json\"},\"fileName\":\"info-schema.json.ts\",\"base\":\"info-schema\",\"nature\":\"json\",\"extn\":\"ts\"}}", "FOUNDRY_MATERIALIZE_BASENAME": "info-schema.auto.json", "FOUNDRY_CONTEXT_JSON": "{\"cliOpts\":{\"dbName\":\"sqlpage.db\",\"cleanDb\":false},\"cwd\":\"/home/<USER>/workspaces/spry/support/assurance/e2e-prime\",\"projectPaths\":{\"projectFsPaths\":{\"identity\":\"project\",\"root\":\"/home/<USER>/workspaces/spry/support/assurance/e2e-prime/\"},\"projectSrcFsPaths\":{\"identity\":\"project-src\",\"root\":\"/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src\"},\"webPaths\":{},\"spryDropIn\":{\"fsHome\":\"/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src/spry.d\",\"fsAuto\":\"/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src/spry.d/auto\",\"webHome\":\"spry.d\",\"webAuto\":\"spry.d/auto\"},\"spryStd\":{\"homeFromSymlink\":\"../../../../lib/std\",\"absPathToLocal\":\"/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src/spry\",\"relPathToHome\":\"src/spry\"},\"sqlPage\":{\"absPathToConfDir\":\"/home/<USER>/workspaces/spry/support/assurance/e2e-prime/sqlpage\"},\"devWatchRoots\":[\"src\",\"../../../lib/std\"]}}", "FOUNDRY_PROJECT_SRC_HOME": "/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src", "FOUNDRY_PROJECT_SPRYD_HOME": "/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src/spry.d", "FOUNDRY_MATERIALIZE_PATH": "/home/<USER>/workspaces/spry/lib/std/lib/info-schema.auto.json", "FOUNDRY_WORKFLOW_STEP": "BEFORE_ANN_CATALOG", "FOUNDRY_AUTO_MATERIALIZE": "TRUE", "FOUNDRY_PROJECT_SPRYD_AUTO": "/home/<USER>/workspaces/spry/support/assurance/e2e-prime/src/spry.d/auto", "FOUNDRY_TARGET_SQLITEDB": "/home/<USER>/workspaces/spry/support/assurance/e2e-prime/sqlpage.db"}}}